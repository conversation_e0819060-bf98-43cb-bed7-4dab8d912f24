"""
row_stitcher_ui.py
UI stitching horizontal row pakai OpenCV (via python-stitching).
Bisa pilih: Stitcher (homography) atau AffineStitcher (affine).
"""

import sys, os
import cv2
from PyQt5 import QtWidgets, QtGui, QtCore
from stitching import Stitcher, AffineStitcher


class RowStitcherApp(QtWidgets.QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Row Stitcher (Feature-based, OpenCV)")
        self.resize(1150, 780)
        self.image_paths = []
        self.panorama = None
        self._setup_ui()

    def _setup_ui(self):
        central = QtWidgets.QWidget()
        self.setCentralWidget(central)
        layout = QtWidgets.QVBoxLayout(central)

        # === Top controls row ===
        ctrl_layout = QtWidgets.QHBoxLayout()
        layout.addLayout(ctrl_layout)

        self.btn_load = QtWidgets.QPushButton("Browse Files")
        self.btn_load.clicked.connect(self.on_load)
        ctrl_layout.addWidget(self.btn_load)

        self.btn_stitch = QtWidgets.QPushButton("Stitch Row")
        self.btn_stitch.clicked.connect(self.on_stitch)
        ctrl_layout.addWidget(self.btn_stitch)

        self.btn_save = QtWidgets.QPushButton("Save Result")
        self.btn_save.clicked.connect(self.on_save)
        ctrl_layout.addWidget(self.btn_save)

        ctrl_layout.addStretch()

        # === Options ===
        options = QtWidgets.QGroupBox("Options")
        opt_layout = QtWidgets.QFormLayout(options)

        # stitcher type
        self.combo_stitcher = QtWidgets.QComboBox()
        self.combo_stitcher.addItems(["Stitcher (homography)", "AffineStitcher (affine)"])
        opt_layout.addRow("Mode:", self.combo_stitcher)

        # detector dropdown
        self.combo_detector = QtWidgets.QComboBox()
        self.combo_detector.addItems(["orb", "sift", "akaze"])
        self.combo_detector.setCurrentText("orb")
        opt_layout.addRow("Detector:", self.combo_detector)

        # confidence threshold slider
        self.slider_conf = QtWidgets.QSlider(QtCore.Qt.Horizontal)
        self.slider_conf.setRange(0, 100)
        self.slider_conf.setValue(60)  # default 0.6
        self.lbl_conf = QtWidgets.QLabel("0.60")
        self.slider_conf.valueChanged.connect(
            lambda v: self.lbl_conf.setText(f"{v/100:.2f}")
        )
        conf_row = QtWidgets.QHBoxLayout()
        conf_row.addWidget(self.slider_conf)
        conf_row.addWidget(self.lbl_conf)
        opt_layout.addRow("Confidence Threshold:", conf_row)

        # blend strength slider
        self.slider_blend = QtWidgets.QSlider(QtCore.Qt.Horizontal)
        self.slider_blend.setRange(1, 20)
        self.slider_blend.setValue(5)
        self.lbl_blend = QtWidgets.QLabel("5")
        self.slider_blend.valueChanged.connect(
            lambda v: self.lbl_blend.setText(str(v))
        )
        blend_row = QtWidgets.QHBoxLayout()
        blend_row.addWidget(self.slider_blend)
        blend_row.addWidget(self.lbl_blend)
        opt_layout.addRow("Blend Strength:", blend_row)

        layout.addWidget(options)

        # === File list ===
        self.list_widget = QtWidgets.QListWidget()
        layout.addWidget(self.list_widget, stretch=1)

        # === Preview label ===
        self.label_preview = QtWidgets.QLabel("Preview")
        self.label_preview.setAlignment(QtCore.Qt.AlignCenter)
        self.label_preview.setMinimumSize(400, 300)
        self.label_preview.setStyleSheet("QLabel { background: #222; color: #ddd; }")
        layout.addWidget(self.label_preview, stretch=3)

        # === Status ===
        self.status = QtWidgets.QLabel("Load images to start")
        layout.addWidget(self.status)

    def log_status(self, text):
        self.status.setText(str(text))
        QtWidgets.QApplication.processEvents()

    def on_load(self):
        files, _ = QtWidgets.QFileDialog.getOpenFileNames(
            self, "Select row images", os.getcwd(),
            "Images (*.png *.jpg *.tif *.tiff *.bmp);;All files (*)"
        )
        if not files:
            return
        self.image_paths = sorted(files)
        self.list_widget.clear()
        for f in self.image_paths:
            self.list_widget.addItem(f)
        self.log_status(f"{len(self.image_paths)} images loaded.")

    def on_stitch(self):
        if not self.image_paths:
            QtWidgets.QMessageBox.warning(self, "No images", "Please load images first.")
            return

        try:
            det = self.combo_detector.currentText()
            conf = self.slider_conf.value() / 100.0
            blend = self.slider_blend.value()
            mode = self.combo_stitcher.currentText()

            self.log_status(f"Stitching [{mode}] with {det}, conf={conf:.2f}, blend={blend}...")

            if "Affine" in mode:
                stitcher = AffineStitcher(
                    detector=det,
                    confidence_threshold=conf,
                    blender_type="multiband",
                    blend_strength=blend,
                    crop=False,
                )
            else:
                stitcher = Stitcher(
                    detector=det,
                    matcher_type="homography",
                    blender_type="multiband",
                    blend_strength=blend,
                    crop=False,
                    confidence_threshold=conf,
                )

            self.panorama = stitcher.stitch(self.image_paths)

            if self.panorama is None:
                raise RuntimeError("Stitching failed.")

            # show preview
            disp = self.panorama.copy()
            h, w = disp.shape[:2]
            max_display = 1000
            scale = min(1.0, max_display / max(w, h))
            disp = cv2.resize(disp, (int(w*scale), int(h*scale)), interpolation=cv2.INTER_AREA)

            qimg = QtGui.QImage(
                cv2.cvtColor(disp, cv2.COLOR_BGR2RGB).data,
                disp.shape[1],
                disp.shape[0],
                3 * disp.shape[1],
                QtGui.QImage.Format_RGB888
            )
            self.label_preview.setPixmap(QtGui.QPixmap.fromImage(qimg))
            self.log_status("Stitching finished.")

        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", str(e))
            self.log_status("Error: " + str(e))

    def on_save(self):
        if self.panorama is None:
            QtWidgets.QMessageBox.warning(self, "No result", "Stitch a row first.")
            return
        save_path, _ = QtWidgets.QFileDialog.getSaveFileName(
            self, "Save panorama", os.path.join(os.getcwd(), "row_stitched.png"),
            "PNG (*.png);;JPEG (*.jpg);;TIFF (*.tif)"
        )
        if not save_path:
            return
        ok = cv2.imwrite(save_path, self.panorama)
        if ok:
            QtWidgets.QMessageBox.information(self, "Saved", f"Saved panorama:\n{save_path}")
        else:
            QtWidgets.QMessageBox.critical(self, "Error", "Failed to save file.")


def main():
    app = QtWidgets.QApplication(sys.argv)
    win = RowStitcherApp()
    win.show()
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
