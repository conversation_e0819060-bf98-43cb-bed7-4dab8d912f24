"""
Grid Stitching (Phase Correlation, Fixed ROI)
--------------------------------------------
UI sederhana untuk stitching gambar mikroskop berbasis grid
dengan refinement overlap pakai phase correlation.
"""

import sys, os
import cv2
import numpy as np
from PyQt5 import QtWidgets, QtGui, QtCore


def phase_correlation_shift(img1, img2):
    """Cari translasi (dx, dy) antara dua gambar grayscale dengan phase correlation."""
    f1 = img1.astype(np.float32)
    f2 = img2.astype(np.float32)
    shift, response = cv2.phaseCorrelate(f1, f2)
    dx, dy = shift  # cv2 balik x,y
    return dx, dy, response


def stitch_grid_phasecorr(images, rows, cols, overlap_x, overlap_y, status_cb=None):
    """
    Stitching grid dengan refinement overlap via phase correlation.
    images: flat list of image paths, length = rows*cols
    """
    # load tiles
    tiles = [cv2.imread(p, cv2.IMREAD_COLOR) for p in images]
    if any(t is None for t in tiles):
        raise IOError("Ada file gambar yang gagal dibaca.")

    h, w = tiles[0].shape[:2]
    step_x = int(w * (1 - overlap_x))
    step_y = int(h * (1 - overlap_y))

    # koordinat tile (initial grid)
    coords = {}
    for r in range(rows):
        for c in range(cols):
            coords[(r, c)] = [c * step_x, r * step_y]  # x,y

    # refine horizontal overlaps
    ox = max(1, int(w * overlap_x))
    oy = max(1, int(h * overlap_y))

    for r in range(rows):
        for c in range(cols - 1):
            idx1 = r * cols + c
            idx2 = r * cols + (c + 1)
            img1 = tiles[idx1]
            img2 = tiles[idx2]

            roi1 = img1[:, w - ox:, :]
            roi2 = img2[:, :ox, :]

            if roi1.shape[0] > 0 and roi2.shape[0] > 0:
                g1 = cv2.cvtColor(roi1, cv2.COLOR_BGR2GRAY)
                g2 = cv2.cvtColor(roi2, cv2.COLOR_BGR2GRAY)

                dx, dy, resp = phase_correlation_shift(g1, g2)
                if status_cb:
                    status_cb(f"Row {r} Col {c}->{c+1}: dx={dx:.2f}, dy={dy:.2f}, resp={resp:.3f}")

                coords[(r, c+1)][0] = coords[(r, c)][0] + step_x + dx
                coords[(r, c+1)][1] = coords[(r, c)][1] + dy

    # refine vertical overlaps
    for r in range(rows - 1):
        for c in range(cols):
            idx1 = r * cols + c
            idx2 = (r + 1) * cols + c
            img1 = tiles[idx1]
            img2 = tiles[idx2]

            roi1 = img1[h - oy:, :, :]
            roi2 = img2[:oy, :, :]

            if roi1.shape[0] > 0 and roi2.shape[0] > 0:
                g1 = cv2.cvtColor(roi1, cv2.COLOR_BGR2GRAY)
                g2 = cv2.cvtColor(roi2, cv2.COLOR_BGR2GRAY)

                dx, dy, resp = phase_correlation_shift(g1, g2)
                if status_cb:
                    status_cb(f"Col {c} Row {r}->{r+1}: dx={dx:.2f}, dy={dy:.2f}, resp={resp:.3f}")

                coords[(r+1, c)][0] = coords[(r, c)][0] + dx
                coords[(r+1, c)][1] = coords[(r, c)][1] + step_y + dy

    # tentukan canvas size
    xs = [coords[(r, c)][0] for r in range(rows) for c in range(cols)]
    ys = [coords[(r, c)][1] for r in range(rows) for c in range(cols)]
    min_x, min_y = int(min(xs)), int(min(ys))
    max_x, max_y = int(max(xs) + w), int(max(ys) + h)

    canvas_w = max_x - min_x
    canvas_h = max_y - min_y
    acc = np.zeros((canvas_h, canvas_w, 3), dtype=np.float32)
    weight = np.zeros((canvas_h, canvas_w), dtype=np.float32)

    # feather mask
    mask_x = np.linspace(0, 1, w).reshape(1, -1)
    mask_y = np.linspace(0, 1, h).reshape(-1, 1)
    mask = np.minimum(
        np.minimum(mask_x, mask_x[:, ::-1]),
        np.minimum(mask_y, mask_y[::-1, :])
    )
    mask = cv2.GaussianBlur(mask, (0, 0), sigmaX=10)

    # place tiles
    for r in range(rows):
        for c in range(cols):
            idx = r * cols + c
            x, y = coords[(r, c)]
            x = int(round(x - min_x))
            y = int(round(y - min_y))
            tile = tiles[idx].astype(np.float32)
            mh, mw = tile.shape[:2]
            acc[y:y+mh, x:x+mw] += tile * mask[..., None]
            weight[y:y+mh, x:x+mw] += mask

    out = np.zeros_like(acc, dtype=np.uint8)
    m = weight > 1e-6
    for ch in range(3):
        out[..., ch][m] = (acc[..., ch][m] / weight[m]).astype(np.uint8)

    return out


class GridStitchUI(QtWidgets.QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Grid Stitching (Phase Correlation)")
        self.resize(1100, 750)
        self.images = []
        self.result = None
        self._setup_ui()

    def _setup_ui(self):
        central = QtWidgets.QWidget()
        self.setCentralWidget(central)
        layout = QtWidgets.QVBoxLayout(central)

        # top buttons
        top = QtWidgets.QHBoxLayout()
        layout.addLayout(top)

        self.btn_load = QtWidgets.QPushButton("Browse Files")
        self.btn_load.clicked.connect(self.on_load)
        top.addWidget(self.btn_load)

        self.btn_stitch = QtWidgets.QPushButton("Stitch Grid")
        self.btn_stitch.clicked.connect(self.on_stitch)
        top.addWidget(self.btn_stitch)

        self.btn_save = QtWidgets.QPushButton("Save Result")
        self.btn_save.clicked.connect(self.on_save)
        top.addWidget(self.btn_save)

        top.addStretch()

        # params
        params = QtWidgets.QGroupBox("Parameters")
        form = QtWidgets.QFormLayout(params)

        self.spin_rows = QtWidgets.QSpinBox()
        self.spin_rows.setRange(1, 50)
        self.spin_rows.setValue(4)
        form.addRow("Rows:", self.spin_rows)

        self.spin_cols = QtWidgets.QSpinBox()
        self.spin_cols.setRange(1, 50)
        self.spin_cols.setValue(9)
        form.addRow("Cols:", self.spin_cols)

        self.spin_ox = QtWidgets.QDoubleSpinBox()
        self.spin_ox.setRange(0.01, 0.9)
        self.spin_ox.setSingleStep(0.05)
        self.spin_ox.setValue(0.1)
        form.addRow("Overlap X:", self.spin_ox)

        self.spin_oy = QtWidgets.QDoubleSpinBox()
        self.spin_oy.setRange(0.01, 0.9)
        self.spin_oy.setSingleStep(0.05)
        self.spin_oy.setValue(0.1)
        form.addRow("Overlap Y:", self.spin_oy)

        layout.addWidget(params)

        # list files
        self.list_files = QtWidgets.QListWidget()
        layout.addWidget(self.list_files, stretch=1)

        # preview
        self.label_preview = QtWidgets.QLabel("Preview")
        self.label_preview.setAlignment(QtCore.Qt.AlignCenter)
        self.label_preview.setMinimumSize(400, 300)
        self.label_preview.setStyleSheet("QLabel { background:#222; color:#ddd; }")
        layout.addWidget(self.label_preview, stretch=3)

        # status
        self.status = QtWidgets.QLabel("Ready")
        layout.addWidget(self.status)

    def log(self, msg):
        self.status.setText(msg)
        QtWidgets.QApplication.processEvents()

    def on_load(self):
        files, _ = QtWidgets.QFileDialog.getOpenFileNames(
            self, "Select grid images", os.getcwd(),
            "Images (*.png *.jpg *.tif *.tiff *.bmp)"
        )
        if not files:
            return
        self.images = sorted(files)
        self.list_files.clear()
        self.list_files.addItems(self.images)
        self.log(f"{len(self.images)} images loaded.")

    def on_stitch(self):
        if not self.images:
            QtWidgets.QMessageBox.warning(self, "No images", "Load images first.")
            return
        rows, cols = self.spin_rows.value(), self.spin_cols.value()
        ox, oy = self.spin_ox.value(), self.spin_oy.value()

        try:
            self.log("Stitching...")
            self.result = stitch_grid_phasecorr(self.images, rows, cols, ox, oy, status_cb=self.log)
            # preview
            disp = self.result.copy()
            h, w = disp.shape[:2]
            scale = min(1.0, 1000 / max(h, w))
            if scale < 1.0:
                disp = cv2.resize(disp, (int(w*scale), int(h*scale)))
            qimg = QtGui.QImage(
                cv2.cvtColor(disp, cv2.COLOR_BGR2RGB).data,
                disp.shape[1], disp.shape[0],
                disp.shape[1]*3, QtGui.QImage.Format_RGB888
            )
            self.label_preview.setPixmap(QtGui.QPixmap.fromImage(qimg))
            self.log("Stitching done.")
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", str(e))
            self.log("Error: " + str(e))

    def on_save(self):
        if self.result is None:
            QtWidgets.QMessageBox.warning(self, "No result", "Stitch first.")
            return
        path, _ = QtWidgets.QFileDialog.getSaveFileName(
            self, "Save result", os.path.join(os.getcwd(), "stitched.png"),
            "PNG (*.png);;JPEG (*.jpg);;TIFF (*.tif)"
        )
        if not path:
            return
        cv2.imwrite(path, self.result)
        self.log(f"Saved to {path}")


def main():
    app = QtWidgets.QApplication(sys.argv)
    win = GridStitchUI()
    win.show()
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
