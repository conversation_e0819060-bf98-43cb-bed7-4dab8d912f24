# MIST GUI - Microscopy Image Stitching Tool

A user-friendly graphical interface for MIST (Microscopy Image Stitching Tool), designed to simplify grid-based image stitching for microscopy applications.

## Features

- **Intuitive GUI**: Easy-to-use interface with tabbed organization
- **Grid Configuration**: Visual grid preview and overlap settings
- **Pattern Matching**: Flexible filename pattern support with placeholders
- **Real-time Validation**: Input validation and error checking
- **Progress Tracking**: Real-time progress monitoring and logging
- **Configuration Management**: Save and load stitching configurations
- **Image Preview**: Visual preview of grid layout and overlaps
- **MIST Integration**: Direct integration with ImageJ/Fiji MIST plugin

## Requirements

### Software Dependencies
- Python 3.7 or higher
- ImageJ or Fiji with MIST plugin installed
- Java Runtime Environment (JRE) 8 or higher

### Python Packages
```bash
pip install -r requirements.txt
```

Required packages:
- `Pillow` (PIL) for image processing
- `numpy` for numerical operations
- `tkinter` (usually included with Python)

## Installation

1. **Clone or download** this repository
2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```
3. **Install ImageJ/Fiji**:
   - Download from [ImageJ](https://imagej.nih.gov/ij/) or [Fiji](https://fiji.sc/)
   - Install the MIST plugin from [NIST website](https://isg.nist.gov/deepzoomweb/software/MIST/index.html)

## Quick Start Demo

### Complete Demo (Recommended for First Time)
```bash
python run_complete_demo.py
```
This will:
- Check all dependencies
- Create test images automatically
- Show usage instructions
- Launch the GUI

### Manual Start
```bash
python mist_gui.py
```

### Create Test Images Only
```bash
python create_test_images.py
```

### Basic Workflow

1. **Input/Output Tab**:
   - Select input directory containing your microscopy images
   - Choose output directory for stitched results
   - Set filename pattern (e.g., `img_{r:02d}_{c:02d}.tif`)
   - Choose output format (TIF, PNG, JPG)

2. **Grid Configuration Tab**:
   - Set grid dimensions (width × height)
   - Configure horizontal and vertical overlap percentages
   - Preview the grid layout

3. **Advanced Settings Tab**:
   - Adjust stage repeatability settings
   - Choose blending method
   - Enable/disable GPU acceleration
   - Configure metadata and preview options

4. **Processing Tab**:
   - Monitor stitching progress
   - View processing logs
   - Track completion status

### Filename Patterns

The GUI supports flexible filename patterns with placeholders:

- `{r}` - Row number (0-based)
- `{c}` - Column number (0-based)
- `{p}` - Position number (1-based)
- `{r:02d}` - Zero-padded row number (e.g., 01, 02, 03...)
- `{c:03d}` - Zero-padded column number with 3 digits

**Examples**:
- `img_{r}_{c}.tif` → `img_0_0.tif`, `img_0_1.tif`, etc.
- `tile_{r:02d}_{c:02d}.png` → `tile_00_00.png`, `tile_00_01.png`, etc.
- `pos_{p:03d}.jpg` → `pos_001.jpg`, `pos_002.jpg`, etc.

### Configuration Files

Save and load stitching configurations as JSON files:

```json
{
    "input_directory": "/path/to/images",
    "output_directory": "/path/to/output",
    "filename_pattern": "img_{r:02d}_{c:02d}.tif",
    "grid_width": 10,
    "grid_height": 8,
    "horizontal_overlap": 15.0,
    "vertical_overlap": 15.0,
    "stage_repeatability": 2.0,
    "blend_method": "LINEAR",
    "output_format": "TIF",
    "use_gpu": true,
    "save_metadata": true,
    "generate_preview": true
}
```

## File Structure

```
mist-gui/
├── mist_gui.py           # Main GUI application
├── mist_integration.py   # MIST/ImageJ integration
├── mist_utils.py         # Utility functions
├── requirements.txt      # Python dependencies
├── README.md            # This file
└── examples/            # Example configurations
    ├── basic_config.json
    └── advanced_config.json
```

## Troubleshooting

### Common Issues

1. **ImageJ/Fiji not found**:
   - Ensure ImageJ or Fiji is installed
   - Check that the executable path is correct
   - Verify MIST plugin is installed in ImageJ

2. **Java not found**:
   - Install Java Runtime Environment (JRE) 8 or higher
   - Ensure Java is in your system PATH

3. **Images not loading**:
   - Check filename pattern matches your images
   - Verify image files are in supported formats
   - Ensure input directory path is correct

4. **Memory issues**:
   - Reduce grid size for large images
   - Enable "Save memory" option in advanced settings
   - Increase Java heap size for ImageJ

### Performance Tips

- **Use GPU acceleration** when available
- **Optimize overlap percentages** (10-20% typically sufficient)
- **Process smaller grids** for very large images
- **Use TIF format** for best quality and compatibility

## Advanced Features

### Custom MIST Integration

The application can be extended to work with different MIST installations:

```python
from mist_integration import MISTIntegration

# Custom ImageJ path
integration = MISTIntegration(
    imagej_path="/custom/path/to/ImageJ",
    mist_plugin_path="/path/to/mist.jar"
)
```

### Batch Processing

For processing multiple datasets, configurations can be loaded and executed programmatically:

```python
import json
from mist_integration import MISTIntegration

# Load configuration
with open('config.json', 'r') as f:
    config = json.load(f)

# Run stitching
integration = MISTIntegration()
success = integration.run_stitching(config)
```

## Contributing

Contributions are welcome! Please feel free to submit issues, feature requests, or pull requests.

### Development Setup

1. Clone the repository
2. Install development dependencies:
   ```bash
   pip install -r requirements.txt
   pip install pytest black flake8  # For testing and code quality
   ```
3. Run tests:
   ```bash
   pytest tests/
   ```

## License

This project is open source. Please check the original MIST software license for any restrictions on the underlying stitching algorithms.

## Acknowledgments

- **MIST Team** at NIST for the excellent stitching algorithm
- **ImageJ/Fiji community** for the powerful microscopy platform
- **Python community** for the excellent libraries used in this project

## Support

For issues related to:
- **GUI application**: Create an issue in this repository
- **MIST algorithm**: Refer to [NIST MIST documentation](https://isg.nist.gov/deepzoomweb/software/MIST/index.html)
- **ImageJ/Fiji**: Check [ImageJ forum](https://forum.image.sc/)

---

**Note**: This GUI is a wrapper around the MIST plugin for ImageJ/Fiji. The actual stitching is performed by the MIST algorithm developed by NIST.
