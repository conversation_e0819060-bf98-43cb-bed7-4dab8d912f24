# MIST GUI - Step-by-Step Usage Guide

## 🚀 Quick Start

### Method 1: Complete Demo (Recommended)
```bash
python run_complete_demo.py
```

### Method 2: Manual Start
```bash
# 1. Create test images first
python create_test_images.py

# 2. Start GUI
python mist_gui.py
```

## 📋 Step-by-Step Tutorial

### Step 1: Start the Application
- Run `python mist_gui.py` or `python run_complete_demo.py`
- The GUI will open with 4 tabs: Input/Output, Grid Configuration, Advanced Settings, Processing

### Step 2: Configure Input/Output (Tab 1)

#### Input Directory
- Click "Browse" next to "Input Directory"
- Navigate to one of the test image folders:
  - `test_images/small_grid` (3×2 grid, good for quick testing)
  - `test_images/medium_grid` (4×3 grid, balanced example)
  - `test_images/large_grid` (6×4 grid, more complex)

#### Output Directory
- Click "Browse" next to "Output Directory"
- Create a new folder or select an empty existing folder
- This is where your stitched results will be saved

#### Filename Pattern
- Use: `tile_{r:02d}_{c:02d}.tif`
- This pattern matches the test images created
- `{r:02d}` = row number with 2 digits (00, 01, 02...)
- `{c:02d}` = column number with 2 digits (00, 01, 02...)

#### Output Format
- Choose TIF (recommended for quality)
- PNG or JPG also available

### Step 3: Configure Grid (Tab 2)

#### Grid Dimensions
Set according to your chosen test set:
- **small_grid**: Width = 3, Height = 2
- **medium_grid**: Width = 4, Height = 3  
- **large_grid**: Width = 6, Height = 4

#### Overlap Settings
- **Horizontal Overlap**: 10-20% (start with 15%)
- **Vertical Overlap**: 10-20% (start with 15%)
- Higher overlap = better stitching but slower processing

#### Grid Preview
- The canvas will show a visual representation of your grid
- Each cell is numbered showing the stitching order
- Preview updates automatically when you change settings

### Step 4: Advanced Settings (Tab 3) - Optional

#### Stage Settings
- **Stage Repeatability**: 2.0 (default is fine)
- This accounts for mechanical precision of the microscope stage

#### Blending Method
- **Linear**: Smooth blending (recommended)
- **Overlay**: Simple overlay
- **Average**: Average pixel values

#### Performance Options
- **Use GPU**: Enable if you have CUDA-capable GPU
- **Save Metadata**: Keep processing information
- **Generate Preview**: Create preview images

### Step 5: Run Processing (Tab 4)

#### Start Stitching
1. Click "Start Stitching" button
2. Monitor the progress bar
3. Watch the log output for detailed information
4. Processing time depends on image size and grid complexity

#### What Happens During Processing

**If ImageJ/MIST is installed:**
- Real MIST processing using ImageJ
- Actual image stitching with advanced algorithms
- Professional-quality results

**If ImageJ/MIST is NOT installed (Demo Mode):**
- Simulation mode with demonstration output
- Creates a sample stitched image showing the grid layout
- Useful for testing the GUI and workflow

#### Monitor Progress
- **Progress Bar**: Shows completion percentage
- **Status**: Current processing step
- **Log Output**: Detailed processing information
- **Time Estimate**: Approximate remaining time

### Step 6: Check Results

#### Output Files
Check your output directory for:
- **Stitched Image**: Main result (e.g., `demo_stitched.tif`)
- **Configuration File**: `stitching_config.json` with all settings used
- **Log Files**: Processing information (if enabled)

#### Demo Mode Results
In demo mode, you'll get:
- A colorful grid image showing the tile layout
- Each tile colored differently to show the stitching pattern
- Position numbers and overlap information displayed

## 🔧 Configuration Management

### Save Configuration
1. Set up all your parameters
2. Click "Save Config" in Input/Output tab
3. Choose filename (e.g., `my_experiment.json`)
4. Configuration saved for future use

### Load Configuration
1. Click "Load Config" in Input/Output tab
2. Select a previously saved configuration file
3. All settings will be restored automatically

### Example Configuration
```json
{
    "input_directory": "test_images/medium_grid",
    "output_directory": "output/experiment_001",
    "filename_pattern": "tile_{r:02d}_{c:02d}.tif",
    "grid_width": 4,
    "grid_height": 3,
    "horizontal_overlap": 15.0,
    "vertical_overlap": 15.0,
    "output_format": "TIF"
}
```

## 🎯 Tips for Best Results

### Choosing Test Sets
- **small_grid**: Quick testing, learning the interface
- **medium_grid**: Good balance of complexity and speed
- **large_grid**: Test performance with larger datasets

### Overlap Settings
- **Too little overlap** (<5%): Poor stitching quality
- **Good overlap** (10-20%): Optimal balance
- **Too much overlap** (>30%): Slower processing, diminishing returns

### Performance Tips
- Start with smaller grids to test settings
- Use TIF format for best quality
- Enable GPU if available
- Monitor memory usage for large grids

### Troubleshooting
- **No output**: Check directory permissions
- **Poor stitching**: Increase overlap percentage
- **Slow processing**: Reduce grid size or image resolution
- **Memory errors**: Use smaller images or enable memory-saving options

## 🔄 Workflow Examples

### Basic Workflow
1. `python run_complete_demo.py`
2. Select `test_images/small_grid`
3. Set grid to 3×2
4. Use 15% overlap
5. Run stitching
6. Check results

### Advanced Workflow
1. Create custom test images
2. Load previous configuration
3. Adjust parameters based on image characteristics
4. Save new configuration
5. Run batch processing
6. Compare results with different settings

## 📊 Understanding Results

### Demo Mode Output
- **Grid Pattern**: Shows how tiles are arranged
- **Color Coding**: Different colors for each tile position
- **Overlap Visualization**: Shows overlap regions
- **Metadata**: Processing parameters used

### Real MIST Output
- **Seamless Image**: Professional stitched result
- **Blend Zones**: Smooth transitions between tiles
- **Geometric Correction**: Corrected for stage positioning errors
- **Quality Metrics**: Stitching accuracy information

## 🚨 Common Issues and Solutions

### GUI Issues
- **Empty fields error**: Ensure all required fields are filled
- **Grid preview not updating**: Check grid dimensions are positive integers
- **Browse buttons not working**: Check file permissions

### Processing Issues
- **ImageJ not found**: Install ImageJ/Fiji or use demo mode
- **Java not found**: Install Java Runtime Environment
- **Memory errors**: Reduce image size or grid dimensions
- **No output generated**: Check output directory permissions

### Image Issues
- **Pattern not matching**: Verify filename pattern syntax
- **Images not loading**: Check image format and file permissions
- **Inconsistent sizes**: Ensure all images have same dimensions

---

**Need Help?**
- Check the main README.md for installation instructions
- Review error messages in the log output
- Try the demo mode first to learn the interface
- Start with small test sets before processing large datasets
