#!/usr/bin/env python3
"""
Create Test Images for MIST GUI
Generates sample microscopy-like images for testing the stitching GUI

Author: AI Assistant
Date: 2025
"""

import os
import numpy as np
from PIL import Image, ImageDraw, ImageFilter
import random
import math

def create_test_images(output_dir, grid_width=4, grid_height=3, image_size=(512, 512), overlap_percent=15):
    """
    Create a set of test images that simulate microscopy tiles
    
    Args:
        output_dir: Directory to save test images
        grid_width: Number of columns in grid
        grid_height: Number of rows in grid
        image_size: Size of each image (width, height)
        overlap_percent: Overlap percentage between adjacent images
    """
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Calculate overlap in pixels
    overlap_x = int(image_size[0] * overlap_percent / 100)
    overlap_y = int(image_size[1] * overlap_percent / 100)
    
    # Calculate effective step size (accounting for overlap)
    step_x = image_size[0] - overlap_x
    step_y = image_size[1] - overlap_y
    
    # Create a large base image to sample from
    total_width = step_x * (grid_width - 1) + image_size[0]
    total_height = step_y * (grid_height - 1) + image_size[1]
    
    print(f"Creating base image of size {total_width}x{total_height}")
    
    # Generate base pattern
    base_image = create_base_pattern(total_width, total_height)
    
    print(f"Generating {grid_width}x{grid_height} = {grid_width * grid_height} test images...")
    
    # Extract tiles from base image
    for row in range(grid_height):
        for col in range(grid_width):
            # Calculate position in base image
            x = col * step_x
            y = row * step_y
            
            # Extract tile
            tile = base_image.crop((x, y, x + image_size[0], y + image_size[1]))
            
            # Add some noise and variation
            tile = add_microscopy_effects(tile, row, col)
            
            # Save tile
            filename = f"tile_{row:02d}_{col:02d}.tif"
            filepath = os.path.join(output_dir, filename)
            tile.save(filepath)
            
            print(f"Created: {filename}")
    
    print(f"\nTest images created in: {output_dir}")
    print(f"Use filename pattern: tile_{{r:02d}}_{{c:02d}}.tif")
    print(f"Grid size: {grid_width} x {grid_height}")
    print(f"Overlap: {overlap_percent}%")

def create_base_pattern(width, height):
    """Create a base pattern that looks like microscopy data"""
    
    # Create base image
    img = Image.new('RGB', (width, height), 'black')
    draw = ImageDraw.Draw(img)
    
    # Add background gradient
    for y in range(height):
        intensity = int(30 + 20 * math.sin(y * math.pi / height))
        color = (intensity, intensity, intensity)
        draw.line([(0, y), (width, y)], fill=color)
    
    # Add some circular "cells" or features
    num_features = (width * height) // 50000  # Density based on image size
    
    for _ in range(num_features):
        # Random position and size
        x = random.randint(0, width)
        y = random.randint(0, height)
        radius = random.randint(10, 50)
        
        # Random color (simulating different stains/fluorescence)
        if random.random() < 0.3:  # 30% chance for bright features
            color = (
                random.randint(100, 255),
                random.randint(100, 255),
                random.randint(100, 255)
            )
        else:  # Darker features
            color = (
                random.randint(40, 120),
                random.randint(40, 120),
                random.randint(40, 120)
            )
        
        # Draw feature
        draw.ellipse([x-radius, y-radius, x+radius, y+radius], fill=color)
        
        # Add some internal structure
        if random.random() < 0.5:
            inner_radius = radius // 2
            inner_color = tuple(max(0, c - 50) for c in color)
            draw.ellipse([x-inner_radius, y-inner_radius, x+inner_radius, y+inner_radius], 
                        fill=inner_color)
    
    # Add some linear structures (like fibers)
    num_lines = width // 100
    for _ in range(num_lines):
        x1 = random.randint(0, width)
        y1 = random.randint(0, height)
        length = random.randint(50, 200)
        angle = random.uniform(0, 2 * math.pi)
        
        x2 = x1 + int(length * math.cos(angle))
        y2 = y1 + int(length * math.sin(angle))
        
        color = (
            random.randint(60, 150),
            random.randint(60, 150),
            random.randint(60, 150)
        )
        
        width_line = random.randint(2, 8)
        draw.line([(x1, y1), (x2, y2)], fill=color, width=width_line)
    
    return img

def add_microscopy_effects(image, row, col):
    """Add effects that simulate real microscopy images"""
    
    # Convert to numpy for easier manipulation
    img_array = np.array(image)
    
    # Add slight illumination variation (common in microscopy)
    center_x, center_y = img_array.shape[1] // 2, img_array.shape[0] // 2
    
    for y in range(img_array.shape[0]):
        for x in range(img_array.shape[1]):
            # Distance from center
            dist = math.sqrt((x - center_x)**2 + (y - center_y)**2)
            max_dist = math.sqrt(center_x**2 + center_y**2)
            
            # Slight vignetting effect
            factor = 1.0 - 0.1 * (dist / max_dist)
            img_array[y, x] = np.clip(img_array[y, x] * factor, 0, 255)
    
    # Add some noise
    noise = np.random.normal(0, 5, img_array.shape)
    img_array = np.clip(img_array + noise, 0, 255).astype(np.uint8)
    
    # Convert back to PIL Image
    result = Image.fromarray(img_array)
    
    # Add slight blur to simulate optical effects
    if random.random() < 0.3:  # 30% chance
        result = result.filter(ImageFilter.GaussianBlur(radius=0.5))
    
    return result

def create_different_test_sets():
    """Create multiple test sets with different characteristics"""
    
    test_sets = [
        {
            "name": "small_grid",
            "grid_width": 3,
            "grid_height": 2,
            "image_size": (256, 256),
            "overlap_percent": 10
        },
        {
            "name": "medium_grid", 
            "grid_width": 4,
            "grid_height": 3,
            "image_size": (512, 512),
            "overlap_percent": 15
        },
        {
            "name": "large_grid",
            "grid_width": 6,
            "grid_height": 4,
            "image_size": (512, 512),
            "overlap_percent": 20
        }
    ]
    
    base_dir = "test_images"
    
    for test_set in test_sets:
        output_dir = os.path.join(base_dir, test_set["name"])
        print(f"\n{'='*50}")
        print(f"Creating test set: {test_set['name']}")
        print(f"{'='*50}")
        
        create_test_images(
            output_dir=output_dir,
            grid_width=test_set["grid_width"],
            grid_height=test_set["grid_height"],
            image_size=test_set["image_size"],
            overlap_percent=test_set["overlap_percent"]
        )

def main():
    """Main function"""
    print("MIST Test Image Generator")
    print("=" * 50)
    
    try:
        # Create different test sets
        create_different_test_sets()
        
        print(f"\n{'='*50}")
        print("All test sets created successfully!")
        print("=" * 50)
        print("\nTo use these images in MIST GUI:")
        print("1. Start the MIST GUI")
        print("2. Select one of the test_images subdirectories as input")
        print("3. Use the filename pattern: tile_{r:02d}_{c:02d}.tif")
        print("4. Set the grid dimensions according to the test set")
        print("5. Run the stitching process")
        
    except Exception as e:
        print(f"Error creating test images: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
