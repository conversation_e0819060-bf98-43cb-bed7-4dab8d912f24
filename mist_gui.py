#!/usr/bin/env python3
"""
MIST GUI - Microscopy Image Stitching Tool GUI
A user-friendly interface for MIST-based grid stitching

Author: AI Assistant
Date: 2025
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import subprocess
import threading
import json
from pathlib import Path
import sys

class MISTGui:
    def __init__(self, root):
        self.root = root
        self.root.title("MIST - Microscopy Image Stitching Tool GUI")
        self.root.geometry("800x700")
        self.root.resizable(True, True)
        
        # Variables
        self.input_dir = tk.StringVar()
        self.output_dir = tk.StringVar()
        self.grid_width = tk.IntVar(value=10)
        self.grid_height = tk.IntVar(value=10)
        self.overlap_percent = tk.DoubleVar(value=10.0)
        self.filename_pattern = tk.StringVar(value="img_{p}_{r}_{c}.tif")
        self.stage_repeatability = tk.DoubleVar(value=2.0)
        self.horizontal_overlap = tk.DoubleVar(value=10.0)
        self.vertical_overlap = tk.DoubleVar(value=10.0)
        self.blend_method = tk.StringVar(value="LINEAR")
        self.output_format = tk.StringVar(value="TIF")
        
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the main UI components"""
        # Create main frame with scrollbar
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create notebook for tabs
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # Input/Output Tab
        self.create_io_tab(notebook)
        
        # Grid Configuration Tab
        self.create_grid_tab(notebook)
        
        # Advanced Settings Tab
        self.create_advanced_tab(notebook)
        
        # Processing Tab
        self.create_processing_tab(notebook)
        
        # Create control buttons
        self.create_control_buttons(main_frame)
        
    def create_io_tab(self, parent):
        """Create Input/Output configuration tab"""
        io_frame = ttk.Frame(parent)
        parent.add(io_frame, text="Input/Output")
        
        # Input directory
        ttk.Label(io_frame, text="Input Directory:", font=("Arial", 10, "bold")).pack(anchor=tk.W, pady=(10,5))
        input_frame = ttk.Frame(io_frame)
        input_frame.pack(fill=tk.X, pady=(0,10))
        
        ttk.Entry(input_frame, textvariable=self.input_dir, width=60).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(input_frame, text="Browse", command=self.browse_input_dir).pack(side=tk.RIGHT, padx=(5,0))
        
        # Output directory
        ttk.Label(io_frame, text="Output Directory:", font=("Arial", 10, "bold")).pack(anchor=tk.W, pady=(10,5))
        output_frame = ttk.Frame(io_frame)
        output_frame.pack(fill=tk.X, pady=(0,10))
        
        ttk.Entry(output_frame, textvariable=self.output_dir, width=60).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(output_frame, text="Browse", command=self.browse_output_dir).pack(side=tk.RIGHT, padx=(5,0))
        
        # Filename pattern
        ttk.Label(io_frame, text="Filename Pattern:", font=("Arial", 10, "bold")).pack(anchor=tk.W, pady=(10,5))
        ttk.Entry(io_frame, textvariable=self.filename_pattern, width=60).pack(fill=tk.X, pady=(0,5))
        
        pattern_help = ttk.Label(io_frame, text="Use {p} for position, {r} for row, {c} for column\nExample: img_{r:02d}_{c:02d}.tif", 
                               foreground="gray", font=("Arial", 8))
        pattern_help.pack(anchor=tk.W, pady=(0,10))
        
        # Output format
        ttk.Label(io_frame, text="Output Format:", font=("Arial", 10, "bold")).pack(anchor=tk.W, pady=(10,5))
        format_combo = ttk.Combobox(io_frame, textvariable=self.output_format, 
                                   values=["TIF", "PNG", "JPG"], state="readonly", width=20)
        format_combo.pack(anchor=tk.W, pady=(0,10))
        
    def create_grid_tab(self, parent):
        """Create Grid configuration tab"""
        grid_frame = ttk.Frame(parent)
        parent.add(grid_frame, text="Grid Configuration")
        
        # Grid dimensions
        dims_frame = ttk.LabelFrame(grid_frame, text="Grid Dimensions", padding=10)
        dims_frame.pack(fill=tk.X, pady=10)
        
        # Grid width
        width_frame = ttk.Frame(dims_frame)
        width_frame.pack(fill=tk.X, pady=5)
        ttk.Label(width_frame, text="Grid Width (columns):").pack(side=tk.LEFT)
        ttk.Spinbox(width_frame, from_=1, to=100, textvariable=self.grid_width, width=10).pack(side=tk.RIGHT)
        
        # Grid height
        height_frame = ttk.Frame(dims_frame)
        height_frame.pack(fill=tk.X, pady=5)
        ttk.Label(height_frame, text="Grid Height (rows):").pack(side=tk.LEFT)
        ttk.Spinbox(height_frame, from_=1, to=100, textvariable=self.grid_height, width=10).pack(side=tk.RIGHT)
        
        # Overlap settings
        overlap_frame = ttk.LabelFrame(grid_frame, text="Overlap Settings", padding=10)
        overlap_frame.pack(fill=tk.X, pady=10)
        
        # Horizontal overlap
        h_overlap_frame = ttk.Frame(overlap_frame)
        h_overlap_frame.pack(fill=tk.X, pady=5)
        ttk.Label(h_overlap_frame, text="Horizontal Overlap (%):").pack(side=tk.LEFT)
        ttk.Spinbox(h_overlap_frame, from_=0, to=50, increment=0.5, 
                   textvariable=self.horizontal_overlap, width=10).pack(side=tk.RIGHT)
        
        # Vertical overlap
        v_overlap_frame = ttk.Frame(overlap_frame)
        v_overlap_frame.pack(fill=tk.X, pady=5)
        ttk.Label(v_overlap_frame, text="Vertical Overlap (%):").pack(side=tk.LEFT)
        ttk.Spinbox(v_overlap_frame, from_=0, to=50, increment=0.5, 
                   textvariable=self.vertical_overlap, width=10).pack(side=tk.RIGHT)
        
        # Preview grid
        preview_frame = ttk.LabelFrame(grid_frame, text="Grid Preview", padding=10)
        preview_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        self.grid_canvas = tk.Canvas(preview_frame, bg="white", height=200)
        self.grid_canvas.pack(fill=tk.BOTH, expand=True)
        
        # Update preview when values change
        self.grid_width.trace('w', self.update_grid_preview)
        self.grid_height.trace('w', self.update_grid_preview)
        
        # Initial preview
        self.update_grid_preview()
        
    def create_advanced_tab(self, parent):
        """Create Advanced settings tab"""
        advanced_frame = ttk.Frame(parent)
        parent.add(advanced_frame, text="Advanced Settings")
        
        # Stage settings
        stage_frame = ttk.LabelFrame(advanced_frame, text="Stage Settings", padding=10)
        stage_frame.pack(fill=tk.X, pady=10)
        
        # Stage repeatability
        repeat_frame = ttk.Frame(stage_frame)
        repeat_frame.pack(fill=tk.X, pady=5)
        ttk.Label(repeat_frame, text="Stage Repeatability (pixels):").pack(side=tk.LEFT)
        ttk.Spinbox(repeat_frame, from_=0.1, to=10.0, increment=0.1, 
                   textvariable=self.stage_repeatability, width=10).pack(side=tk.RIGHT)
        
        # Blending method
        blend_frame = ttk.LabelFrame(advanced_frame, text="Blending Options", padding=10)
        blend_frame.pack(fill=tk.X, pady=10)
        
        blend_method_frame = ttk.Frame(blend_frame)
        blend_method_frame.pack(fill=tk.X, pady=5)
        ttk.Label(blend_method_frame, text="Blending Method:").pack(side=tk.LEFT)
        blend_combo = ttk.Combobox(blend_method_frame, textvariable=self.blend_method,
                                  values=["LINEAR", "OVERLAY", "AVERAGE"], state="readonly", width=15)
        blend_combo.pack(side=tk.RIGHT)
        
        # Additional options
        options_frame = ttk.LabelFrame(advanced_frame, text="Processing Options", padding=10)
        options_frame.pack(fill=tk.X, pady=10)
        
        self.use_gpu = tk.BooleanVar(value=True)
        self.save_metadata = tk.BooleanVar(value=True)
        self.generate_preview = tk.BooleanVar(value=True)
        
        ttk.Checkbutton(options_frame, text="Use GPU acceleration", variable=self.use_gpu).pack(anchor=tk.W, pady=2)
        ttk.Checkbutton(options_frame, text="Save metadata", variable=self.save_metadata).pack(anchor=tk.W, pady=2)
        ttk.Checkbutton(options_frame, text="Generate preview image", variable=self.generate_preview).pack(anchor=tk.W, pady=2)
        
    def create_processing_tab(self, parent):
        """Create Processing tab with log output"""
        process_frame = ttk.Frame(parent)
        parent.add(process_frame, text="Processing")
        
        # Progress bar
        ttk.Label(process_frame, text="Processing Progress:", font=("Arial", 10, "bold")).pack(anchor=tk.W, pady=(10,5))
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(process_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill=tk.X, pady=(0,10))
        
        # Status label
        self.status_var = tk.StringVar(value="Ready")
        status_label = ttk.Label(process_frame, textvariable=self.status_var, font=("Arial", 9))
        status_label.pack(anchor=tk.W, pady=(0,10))
        
        # Log output
        ttk.Label(process_frame, text="Processing Log:", font=("Arial", 10, "bold")).pack(anchor=tk.W, pady=(10,5))
        self.log_text = scrolledtext.ScrolledText(process_frame, height=20, width=80)
        self.log_text.pack(fill=tk.BOTH, expand=True, pady=(0,10))
        
    def create_control_buttons(self, parent):
        """Create control buttons"""
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(10,0))
        
        # Left side buttons
        left_buttons = ttk.Frame(button_frame)
        left_buttons.pack(side=tk.LEFT)
        
        ttk.Button(left_buttons, text="Load Config", command=self.load_config).pack(side=tk.LEFT, padx=(0,5))
        ttk.Button(left_buttons, text="Save Config", command=self.save_config).pack(side=tk.LEFT, padx=(0,5))
        
        # Right side buttons
        right_buttons = ttk.Frame(button_frame)
        right_buttons.pack(side=tk.RIGHT)
        
        ttk.Button(right_buttons, text="Validate", command=self.validate_inputs).pack(side=tk.LEFT, padx=(0,5))
        ttk.Button(right_buttons, text="Start Stitching", command=self.start_stitching, 
                  style="Accent.TButton").pack(side=tk.LEFT, padx=(0,5))
        ttk.Button(right_buttons, text="Stop", command=self.stop_processing).pack(side=tk.LEFT)
        
    def browse_input_dir(self):
        """Browse for input directory"""
        directory = filedialog.askdirectory(title="Select Input Directory")
        if directory:
            self.input_dir.set(directory)
            
    def browse_output_dir(self):
        """Browse for output directory"""
        directory = filedialog.askdirectory(title="Select Output Directory")
        if directory:
            self.output_dir.set(directory)
            
    def update_grid_preview(self, *args):
        """Update the grid preview canvas"""
        self.grid_canvas.delete("all")
        
        canvas_width = self.grid_canvas.winfo_width()
        canvas_height = self.grid_canvas.winfo_height()
        
        if canvas_width <= 1 or canvas_height <= 1:
            self.root.after(100, self.update_grid_preview)
            return
            
        cols = self.grid_width.get()
        rows = self.grid_height.get()
        
        if cols <= 0 or rows <= 0:
            return
            
        # Calculate cell size
        margin = 20
        cell_width = (canvas_width - 2 * margin) / cols
        cell_height = (canvas_height - 2 * margin) / rows
        
        # Draw grid
        for r in range(rows):
            for c in range(cols):
                x1 = margin + c * cell_width
                y1 = margin + r * cell_height
                x2 = x1 + cell_width
                y2 = y1 + cell_height
                
                # Draw rectangle
                self.grid_canvas.create_rectangle(x1, y1, x2, y2, outline="blue", width=1)
                
                # Draw position number
                pos = r * cols + c + 1
                self.grid_canvas.create_text(x1 + cell_width/2, y1 + cell_height/2,
                                           text=str(pos), font=("Arial", 8))

    def validate_inputs(self):
        """Validate user inputs"""
        errors = []

        # Check input directory
        if not self.input_dir.get():
            errors.append("Input directory is required")
        elif not os.path.exists(self.input_dir.get()):
            errors.append("Input directory does not exist")

        # Check output directory
        if not self.output_dir.get():
            errors.append("Output directory is required")
        else:
            # Create output directory if it doesn't exist
            try:
                os.makedirs(self.output_dir.get(), exist_ok=True)
            except Exception as e:
                errors.append(f"Cannot create output directory: {str(e)}")

        # Check grid dimensions
        if self.grid_width.get() <= 0 or self.grid_height.get() <= 0:
            errors.append("Grid dimensions must be positive")

        # Check overlap values
        if not (0 <= self.horizontal_overlap.get() <= 50):
            errors.append("Horizontal overlap must be between 0 and 50%")
        if not (0 <= self.vertical_overlap.get() <= 50):
            errors.append("Vertical overlap must be between 0 and 50%")

        if errors:
            messagebox.showerror("Validation Error", "\n".join(errors))
            return False
        else:
            messagebox.showinfo("Validation", "All inputs are valid!")
            return True

    def generate_mist_config(self):
        """Generate MIST configuration"""
        config = {
            "input_directory": self.input_dir.get(),
            "output_directory": self.output_dir.get(),
            "filename_pattern": self.filename_pattern.get(),
            "grid_width": self.grid_width.get(),
            "grid_height": self.grid_height.get(),
            "horizontal_overlap": self.horizontal_overlap.get(),
            "vertical_overlap": self.vertical_overlap.get(),
            "stage_repeatability": self.stage_repeatability.get(),
            "blend_method": self.blend_method.get(),
            "output_format": self.output_format.get(),
            "use_gpu": self.use_gpu.get(),
            "save_metadata": self.save_metadata.get(),
            "generate_preview": self.generate_preview.get()
        }
        return config

    def save_config(self):
        """Save current configuration to file"""
        config = self.generate_mist_config()

        filename = filedialog.asksaveasfilename(
            title="Save Configuration",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'w') as f:
                    json.dump(config, f, indent=4)
                messagebox.showinfo("Success", f"Configuration saved to {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save configuration: {str(e)}")

    def load_config(self):
        """Load configuration from file"""
        filename = filedialog.askopenfilename(
            title="Load Configuration",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'r') as f:
                    config = json.load(f)

                # Update GUI with loaded values
                self.input_dir.set(config.get("input_directory", ""))
                self.output_dir.set(config.get("output_directory", ""))
                self.filename_pattern.set(config.get("filename_pattern", "img_{r}_{c}.tif"))
                self.grid_width.set(config.get("grid_width", 10))
                self.grid_height.set(config.get("grid_height", 10))
                self.horizontal_overlap.set(config.get("horizontal_overlap", 10.0))
                self.vertical_overlap.set(config.get("vertical_overlap", 10.0))
                self.stage_repeatability.set(config.get("stage_repeatability", 2.0))
                self.blend_method.set(config.get("blend_method", "LINEAR"))
                self.output_format.set(config.get("output_format", "TIF"))
                self.use_gpu.set(config.get("use_gpu", True))
                self.save_metadata.set(config.get("save_metadata", True))
                self.generate_preview.set(config.get("generate_preview", True))

                messagebox.showinfo("Success", f"Configuration loaded from {filename}")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to load configuration: {str(e)}")

    def log_message(self, message):
        """Add message to log"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def update_status(self, status):
        """Update status label"""
        self.status_var.set(status)
        self.root.update_idletasks()

    def update_progress(self, value):
        """Update progress bar"""
        self.progress_var.set(value)
        self.root.update_idletasks()

    def start_stitching(self):
        """Start the stitching process"""
        if not self.validate_inputs():
            return

        # Switch to processing tab
        notebook = self.root.children['!frame'].children['!notebook']
        notebook.select(3)  # Processing tab

        # Clear log
        self.log_text.delete(1.0, tk.END)

        # Start processing in separate thread
        self.processing_thread = threading.Thread(target=self.run_stitching)
        self.processing_thread.daemon = True
        self.processing_thread.start()

    def run_stitching(self):
        """Run the actual stitching process"""
        try:
            self.update_status("Preparing stitching...")
            self.update_progress(10)
            self.log_message("Starting MIST stitching process...")

            config = self.generate_mist_config()

            # Create MIST command (this is a simplified example)
            # In practice, you would need to interface with actual MIST software
            self.simulate_mist_processing(config)

        except Exception as e:
            self.log_message(f"Error: {str(e)}")
            self.update_status("Error occurred")
            messagebox.showerror("Processing Error", str(e))

    def simulate_mist_processing(self, config):
        """Simulate MIST processing (replace with actual MIST integration)"""
        import time

        steps = [
            "Scanning input directory...",
            "Loading image metadata...",
            "Computing pairwise translations...",
            "Optimizing global positions...",
            "Blending overlapping regions...",
            "Generating output image...",
            "Saving results..."
        ]

        for i, step in enumerate(steps):
            self.log_message(step)
            self.update_status(step)
            progress = 20 + (i + 1) * (80 / len(steps))
            self.update_progress(progress)
            time.sleep(1)  # Simulate processing time

        self.update_progress(100)
        self.update_status("Stitching completed successfully!")
        self.log_message("Stitching process completed successfully!")
        self.log_message(f"Output saved to: {config['output_directory']}")

        messagebox.showinfo("Success", "Stitching completed successfully!")

    def stop_processing(self):
        """Stop the processing"""
        self.update_status("Stopping...")
        self.log_message("Processing stopped by user")
        # In practice, you would terminate the actual MIST process here

def main():
    """Main function to run the GUI"""
    root = tk.Tk()

    # Set style
    style = ttk.Style()
    style.theme_use('clam')

    # Create and run the application
    app = MISTGui(root)

    # Center window on screen
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")

    root.mainloop()

if __name__ == "__main__":
    main()
