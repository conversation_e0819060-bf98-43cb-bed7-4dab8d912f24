#!/usr/bin/env python3
"""
MIST Integration Module
Handles the actual integration with MIST Python implementation

Author: AI Assistant
Date: 2025
"""

import os
import sys
import json
import tempfile
import shutil
from pathlib import Path
import logging
import argparse
from typing import Dict, List, Optional, Callable

class MISTIntegration:
    """Class to handle MIST Python integration"""

    def __init__(self, mist_path: Optional[str] = None):
        """
        Initialize MIST integration

        Args:
            mist_path: Path to MIST Python module directory
        """
        self.mist_path = mist_path or self.find_mist_python()
        self.logger = logging.getLogger(__name__)

        # Setup logging
        logging.basicConfig(level=logging.INFO)

        # Add MIST path to Python path if found
        if self.mist_path and self.mist_path not in sys.path:
            sys.path.insert(0, self.mist_path)

    def find_mist_python(self) -> Optional[str]:
        """Try to find MIST Python module automatically"""
        possible_paths = [
            # Current directory
            os.path.join(os.getcwd(), "mist_stitching"),
            # Parent directory
            os.path.join(os.path.dirname(os.getcwd()), "mist_stitching"),
            # Common installation paths
            os.path.join(os.path.expanduser("~"), "MIST-python", "src", "mist_stitching"),
            os.path.join(os.path.expanduser("~"), "mist_stitching"),
        ]

        for path in possible_paths:
            if os.path.exists(path) and os.path.exists(os.path.join(path, "main.py")):
                return path

        return None
        
    def validate_installation(self) -> Dict[str, bool]:
        """Validate MIST Python installation and dependencies"""
        validation = {
            "mist_python_found": False,
            "mist_importable": False,
            "dependencies_available": False
        }

        try:
            from mist_python_wrapper import MISTPythonWrapper
            wrapper = MISTPythonWrapper(self.mist_path)

            # Use wrapper for validation
            validation["mist_python_found"] = wrapper.validate_mist_python()
            validation["mist_importable"] = validation["mist_python_found"]

        except ImportError:
            # Fallback validation
            if self.mist_path and os.path.exists(self.mist_path):
                validation["mist_python_found"] = True

        # Check dependencies
        try:
            import numpy
            import scipy
            import skimage
            from PIL import Image
            validation["dependencies_available"] = True
        except ImportError:
            pass

        return validation
        
    def create_mist_args(self, config: Dict) -> argparse.Namespace:
        """Create MIST Python arguments from configuration"""

        # Convert filename pattern to MIST format
        filename_pattern = config['filename_pattern']

        # Determine pattern type based on placeholders
        if '{r}' in filename_pattern or '{c}' in filename_pattern:
            pattern_type = 'ROWCOL'
            # Convert {r:02d} to {rrr} format for MIST
            filename_pattern = filename_pattern.replace('{r:02d}', '{rrr}')
            filename_pattern = filename_pattern.replace('{c:02d}', '{ccc}')
            filename_pattern = filename_pattern.replace('{r}', '{r}')
            filename_pattern = filename_pattern.replace('{c}', '{c}')
        else:
            pattern_type = 'SEQUENTIAL'

        # Create arguments namespace
        args = argparse.Namespace()

        # Required arguments
        args.image_dirpath = config['input_directory']
        args.output_dirpath = config['output_directory']
        args.grid_width = config['grid_width']
        args.grid_height = config['grid_height']
        args.filename_pattern = filename_pattern
        args.filename_pattern_type = pattern_type

        # Default arguments
        args.start_tile = 0
        args.start_row = 0
        args.start_col = 0
        args.grid_origin = 'UL'  # Upper Left
        args.numbering_pattern = 'HORIZONTALCOMBING'
        args.output_prefix = 'img-'
        args.save_image = True
        args.disable_mem_cache = False

        # Stage model parameters
        args.stage_repeatability = config.get('stage_repeatability', 2.0)
        args.horizontal_overlap = config.get('horizontal_overlap', 10.0)
        args.vertical_overlap = config.get('vertical_overlap', 10.0)
        args.overlap_uncertainty = 3.0
        args.valid_correlation_threshold = 0.5
        args.time_slice = 0

        # Advanced parameters
        args.translation_refinement_method = 'SINGLEHILLCLIMB'
        args.num_hill_climbs = 16
        args.num_fft_peaks = 2

        return args
        
    def run_stitching(self, config: Dict, progress_callback: Optional[Callable] = None,
                     log_callback: Optional[Callable] = None) -> bool:
        """
        Run MIST Python stitching with given configuration

        Args:
            config: Stitching configuration dictionary
            progress_callback: Function to call with progress updates (0-100)
            log_callback: Function to call with log messages

        Returns:
            True if successful, False otherwise
        """

        def log(message: str):
            self.logger.info(message)
            if log_callback:
                log_callback(message)

        def update_progress(value: int):
            if progress_callback:
                progress_callback(value)

        try:
            # Use MIST Python wrapper for safer execution
            from mist_python_wrapper import MISTPythonWrapper

            wrapper = MISTPythonWrapper(self.mist_path)

            log("Starting MIST Python stitching process...")
            update_progress(10)

            # Run stitching using wrapper
            success = wrapper.run_mist_stitching(config, progress_callback, log_callback)

            if success:
                log("MIST stitching completed successfully!")
                return True
            else:
                log("MIST stitching failed")
                return False

        except Exception as e:
            log(f"Error during stitching: {str(e)}")
            import traceback
            log(f"Traceback: {traceback.format_exc()}")
            return False
                
    def get_input_images(self, input_dir: str, pattern: str) -> List[str]:
        """Get list of input images matching the pattern"""
        input_path = Path(input_dir)
        
        if not input_path.exists():
            return []
            
        # Simple pattern matching (can be enhanced)
        if '{' in pattern:
            # Pattern with placeholders - find all matching files
            base_pattern = pattern.split('{')[0]
            extension = pattern.split('.')[-1]
            
            matching_files = []
            for file in input_path.glob(f"*{base_pattern}*.{extension}"):
                matching_files.append(str(file))
                
            return sorted(matching_files)
        else:
            # Direct pattern
            return sorted([str(f) for f in input_path.glob(pattern)])
            
    def estimate_processing_time(self, config: Dict) -> float:
        """Estimate processing time based on configuration"""
        # Get input images
        images = self.get_input_images(config['input_directory'], config['filename_pattern'])
        num_images = len(images)
        
        if num_images == 0:
            return 0.0
            
        # Rough estimation based on number of images and overlap
        base_time = num_images * 2  # 2 seconds per image base time
        overlap_factor = (config['horizontal_overlap'] + config['vertical_overlap']) / 20.0
        complexity_factor = 1 + overlap_factor
        
        estimated_time = base_time * complexity_factor
        
        return max(10.0, estimated_time)  # Minimum 10 seconds
        
    def check_memory_requirements(self, config: Dict) -> Dict[str, float]:
        """Estimate memory requirements for stitching"""
        images = self.get_input_images(config['input_directory'], config['filename_pattern'])
        
        if not images:
            return {"estimated_mb": 0, "recommended_mb": 1024}
            
        # Try to get image dimensions from first image
        try:
            from PIL import Image
            with Image.open(images[0]) as img:
                width, height = img.size
                channels = len(img.getbands())
                
            # Estimate memory usage
            pixels_per_image = width * height * channels
            total_pixels = pixels_per_image * len(images)
            
            # Rough estimation: 4 bytes per pixel for processing
            estimated_mb = (total_pixels * 4) / (1024 * 1024)
            recommended_mb = estimated_mb * 2  # 2x for safety
            
            return {
                "estimated_mb": estimated_mb,
                "recommended_mb": max(1024, recommended_mb)
            }
            
        except Exception:
            # Fallback estimation
            return {
                "estimated_mb": len(images) * 50,  # 50MB per image estimate
                "recommended_mb": max(1024, len(images) * 100)
            }

class MISTValidator:
    """Utility class for validating MIST configurations and inputs"""
    
    @staticmethod
    def validate_config(config: Dict) -> List[str]:
        """Validate MIST configuration and return list of errors"""
        errors = []
        
        # Required fields
        required_fields = ['input_directory', 'output_directory', 'filename_pattern',
                          'grid_width', 'grid_height']
        
        for field in required_fields:
            if field not in config or not config[field]:
                errors.append(f"Missing required field: {field}")
                
        # Directory validation
        if 'input_directory' in config:
            if not os.path.exists(config['input_directory']):
                errors.append("Input directory does not exist")
                
        # Numeric validation
        if 'grid_width' in config:
            if not isinstance(config['grid_width'], int) or config['grid_width'] <= 0:
                errors.append("Grid width must be a positive integer")
                
        if 'grid_height' in config:
            if not isinstance(config['grid_height'], int) or config['grid_height'] <= 0:
                errors.append("Grid height must be a positive integer")
                
        # Overlap validation
        for overlap_field in ['horizontal_overlap', 'vertical_overlap']:
            if overlap_field in config:
                value = config[overlap_field]
                if not isinstance(value, (int, float)) or not (0 <= value <= 50):
                    errors.append(f"{overlap_field} must be between 0 and 50")
                    
        return errors
        
    @staticmethod
    def validate_input_images(input_dir: str, pattern: str) -> Dict[str, any]:
        """Validate input images and return validation results"""
        result = {
            "valid": False,
            "image_count": 0,
            "errors": [],
            "warnings": [],
            "image_info": {}
        }
        
        try:
            integration = MISTIntegration()
            images = integration.get_input_images(input_dir, pattern)
            
            result["image_count"] = len(images)
            
            if len(images) == 0:
                result["errors"].append("No images found matching the pattern")
                return result
                
            # Check first few images for consistency
            from PIL import Image
            
            first_img_info = None
            inconsistent_sizes = []
            
            for i, img_path in enumerate(images[:5]):  # Check first 5 images
                try:
                    with Image.open(img_path) as img:
                        img_info = {
                            "size": img.size,
                            "mode": img.mode,
                            "format": img.format
                        }
                        
                        if first_img_info is None:
                            first_img_info = img_info
                            result["image_info"] = img_info
                        elif img_info["size"] != first_img_info["size"]:
                            inconsistent_sizes.append(img_path)
                            
                except Exception as e:
                    result["errors"].append(f"Cannot read image {img_path}: {str(e)}")
                    
            if inconsistent_sizes:
                result["warnings"].append(f"Inconsistent image sizes found in {len(inconsistent_sizes)} images")
                
            if len(result["errors"]) == 0:
                result["valid"] = True
                
        except Exception as e:
            result["errors"].append(f"Error validating images: {str(e)}")
            
        return result
