#!/usr/bin/env python3
"""
MIST Integration Module
Handles the actual integration with MIST software and ImageJ

Author: AI Assistant
Date: 2025
"""

import os
import subprocess
import json
import tempfile
import shutil
from pathlib import Path
import logging
from typing import Dict, List, Optional, Callable

class MISTIntegration:
    """Class to handle MIST software integration"""
    
    def __init__(self, imagej_path: Optional[str] = None, mist_plugin_path: Optional[str] = None):
        """
        Initialize MIST integration
        
        Args:
            imagej_path: Path to ImageJ/Fiji executable
            mist_plugin_path: Path to MIST plugin jar file
        """
        self.imagej_path = imagej_path or self.find_imagej()
        self.mist_plugin_path = mist_plugin_path
        self.logger = logging.getLogger(__name__)
        
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        
    def find_imagej(self) -> Optional[str]:
        """Try to find ImageJ/Fiji installation automatically"""
        possible_paths = [
            # Windows
            r"C:\Program Files\ImageJ\ImageJ.exe",
            r"C:\Program Files\Fiji.app\ImageJ-win64.exe",
            r"C:\Users\<USER>\AppData\Local\Fiji.app\ImageJ-win64.exe".format(os.getenv('USERNAME', '')),
            # macOS
            "/Applications/ImageJ.app/Contents/MacOS/JavaApplicationStub",
            "/Applications/Fiji.app/Contents/MacOS/ImageJ-macosx",
            # Linux
            "/usr/bin/imagej",
            "/opt/ImageJ/ImageJ",
            "/opt/Fiji.app/ImageJ-linux64"
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
                
        return None
        
    def validate_installation(self) -> Dict[str, bool]:
        """Validate MIST installation and dependencies"""
        validation = {
            "imagej_found": False,
            "mist_plugin_found": False,
            "java_available": False
        }
        
        # Check ImageJ
        if self.imagej_path and os.path.exists(self.imagej_path):
            validation["imagej_found"] = True
            
        # Check Java
        try:
            result = subprocess.run(["java", "-version"], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                validation["java_available"] = True
        except (subprocess.TimeoutExpired, FileNotFoundError):
            pass
            
        # Check MIST plugin
        if self.mist_plugin_path and os.path.exists(self.mist_plugin_path):
            validation["mist_plugin_found"] = True
            
        return validation
        
    def create_mist_macro(self, config: Dict) -> str:
        """Create ImageJ macro for MIST stitching"""
        
        # Convert Python config to MIST parameters
        macro_template = '''
// MIST Stitching Macro
// Generated automatically

// Set batch mode for faster processing
setBatchMode(true);

// MIST Plugin Parameters
inputDir = "{input_dir}";
outputDir = "{output_dir}";
filenamePattern = "{filename_pattern}";
gridWidth = {grid_width};
gridHeight = {grid_height};
horizontalOverlap = {horizontal_overlap};
verticalOverlap = {vertical_overlap};
stageRepeatability = {stage_repeatability};
outputFormat = "{output_format}";

// Run MIST plugin
run("Grid/Collection stitching", 
    "type=[Grid: row-by-row] " +
    "order=[Right & Down] " +
    "directory=[" + inputDir + "] " +
    "file_names=[" + filenamePattern + "] " +
    "output_textfile_name=TileConfiguration.txt " +
    "fusion_method=[Linear Blending] " +
    "regression_threshold=0.30 " +
    "max/avg_displacement_threshold=2.50 " +
    "absolute_displacement_threshold=3.50 " +
    "compute_overlap " +
    "computation_parameters=[Save memory (but be slower)] " +
    "image_output=[Fuse and display]");

// Save result
if (nImages > 0) {{
    selectImage(nImages);
    saveAs("{output_format}", outputDir + "/stitched_result.{ext}");
    close();
}}

// Exit batch mode
setBatchMode(false);

print("MIST stitching completed successfully!");
'''
        
        # Format the macro with config values
        ext = config['output_format'].lower()
        if ext == 'tif':
            ext = 'tiff'
            
        macro = macro_template.format(
            input_dir=config['input_directory'].replace('\\', '/'),
            output_dir=config['output_directory'].replace('\\', '/'),
            filename_pattern=config['filename_pattern'],
            grid_width=config['grid_width'],
            grid_height=config['grid_height'],
            horizontal_overlap=config['horizontal_overlap'],
            vertical_overlap=config['vertical_overlap'],
            stage_repeatability=config['stage_repeatability'],
            output_format=config['output_format'],
            ext=ext
        )
        
        return macro
        
    def run_stitching(self, config: Dict, progress_callback: Optional[Callable] = None,
                     log_callback: Optional[Callable] = None) -> bool:
        """
        Run MIST stitching with given configuration
        
        Args:
            config: Stitching configuration dictionary
            progress_callback: Function to call with progress updates (0-100)
            log_callback: Function to call with log messages
            
        Returns:
            True if successful, False otherwise
        """
        
        def log(message: str):
            self.logger.info(message)
            if log_callback:
                log_callback(message)
                
        def update_progress(value: int):
            if progress_callback:
                progress_callback(value)
                
        try:
            # Validate installation
            validation = self.validate_installation()
            if not validation["imagej_found"]:
                raise Exception("ImageJ/Fiji not found. Please install ImageJ or Fiji.")
                
            if not validation["java_available"]:
                raise Exception("Java not found. Please install Java.")
                
            log("Starting MIST stitching process...")
            update_progress(10)
            
            # Create temporary macro file
            macro_content = self.create_mist_macro(config)
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.ijm', delete=False) as f:
                f.write(macro_content)
                macro_path = f.name
                
            log(f"Created ImageJ macro: {macro_path}")
            update_progress(20)
            
            # Prepare ImageJ command
            cmd = [
                self.imagej_path,
                "--headless",
                "--console",
                "-macro", macro_path
            ]
            
            log("Executing ImageJ with MIST plugin...")
            update_progress(30)
            
            # Run ImageJ
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                cwd=config['output_directory']
            )
            
            # Monitor progress
            while process.poll() is None:
                # Read output
                output = process.stdout.readline()
                if output:
                    log(f"ImageJ: {output.strip()}")
                    
                # Update progress (simplified)
                update_progress(min(90, progress_callback and progress_callback.get_current() + 5 or 50))
                
            # Get final output
            stdout, stderr = process.communicate()
            
            if stdout:
                log(f"ImageJ output: {stdout}")
            if stderr:
                log(f"ImageJ errors: {stderr}")
                
            # Check if process was successful
            if process.returncode == 0:
                log("MIST stitching completed successfully!")
                update_progress(100)
                return True
            else:
                log(f"ImageJ process failed with return code: {process.returncode}")
                return False
                
        except Exception as e:
            log(f"Error during stitching: {str(e)}")
            return False
            
        finally:
            # Clean up temporary files
            try:
                if 'macro_path' in locals():
                    os.unlink(macro_path)
            except:
                pass
                
    def get_input_images(self, input_dir: str, pattern: str) -> List[str]:
        """Get list of input images matching the pattern"""
        input_path = Path(input_dir)
        
        if not input_path.exists():
            return []
            
        # Simple pattern matching (can be enhanced)
        if '{' in pattern:
            # Pattern with placeholders - find all matching files
            base_pattern = pattern.split('{')[0]
            extension = pattern.split('.')[-1]
            
            matching_files = []
            for file in input_path.glob(f"*{base_pattern}*.{extension}"):
                matching_files.append(str(file))
                
            return sorted(matching_files)
        else:
            # Direct pattern
            return sorted([str(f) for f in input_path.glob(pattern)])
            
    def estimate_processing_time(self, config: Dict) -> float:
        """Estimate processing time based on configuration"""
        # Get input images
        images = self.get_input_images(config['input_directory'], config['filename_pattern'])
        num_images = len(images)
        
        if num_images == 0:
            return 0.0
            
        # Rough estimation based on number of images and overlap
        base_time = num_images * 2  # 2 seconds per image base time
        overlap_factor = (config['horizontal_overlap'] + config['vertical_overlap']) / 20.0
        complexity_factor = 1 + overlap_factor
        
        estimated_time = base_time * complexity_factor
        
        return max(10.0, estimated_time)  # Minimum 10 seconds
        
    def check_memory_requirements(self, config: Dict) -> Dict[str, float]:
        """Estimate memory requirements for stitching"""
        images = self.get_input_images(config['input_directory'], config['filename_pattern'])
        
        if not images:
            return {"estimated_mb": 0, "recommended_mb": 1024}
            
        # Try to get image dimensions from first image
        try:
            from PIL import Image
            with Image.open(images[0]) as img:
                width, height = img.size
                channels = len(img.getbands())
                
            # Estimate memory usage
            pixels_per_image = width * height * channels
            total_pixels = pixels_per_image * len(images)
            
            # Rough estimation: 4 bytes per pixel for processing
            estimated_mb = (total_pixels * 4) / (1024 * 1024)
            recommended_mb = estimated_mb * 2  # 2x for safety
            
            return {
                "estimated_mb": estimated_mb,
                "recommended_mb": max(1024, recommended_mb)
            }
            
        except Exception:
            # Fallback estimation
            return {
                "estimated_mb": len(images) * 50,  # 50MB per image estimate
                "recommended_mb": max(1024, len(images) * 100)
            }

class MISTValidator:
    """Utility class for validating MIST configurations and inputs"""
    
    @staticmethod
    def validate_config(config: Dict) -> List[str]:
        """Validate MIST configuration and return list of errors"""
        errors = []
        
        # Required fields
        required_fields = ['input_directory', 'output_directory', 'filename_pattern',
                          'grid_width', 'grid_height']
        
        for field in required_fields:
            if field not in config or not config[field]:
                errors.append(f"Missing required field: {field}")
                
        # Directory validation
        if 'input_directory' in config:
            if not os.path.exists(config['input_directory']):
                errors.append("Input directory does not exist")
                
        # Numeric validation
        if 'grid_width' in config:
            if not isinstance(config['grid_width'], int) or config['grid_width'] <= 0:
                errors.append("Grid width must be a positive integer")
                
        if 'grid_height' in config:
            if not isinstance(config['grid_height'], int) or config['grid_height'] <= 0:
                errors.append("Grid height must be a positive integer")
                
        # Overlap validation
        for overlap_field in ['horizontal_overlap', 'vertical_overlap']:
            if overlap_field in config:
                value = config[overlap_field]
                if not isinstance(value, (int, float)) or not (0 <= value <= 50):
                    errors.append(f"{overlap_field} must be between 0 and 50")
                    
        return errors
        
    @staticmethod
    def validate_input_images(input_dir: str, pattern: str) -> Dict[str, any]:
        """Validate input images and return validation results"""
        result = {
            "valid": False,
            "image_count": 0,
            "errors": [],
            "warnings": [],
            "image_info": {}
        }
        
        try:
            integration = MISTIntegration()
            images = integration.get_input_images(input_dir, pattern)
            
            result["image_count"] = len(images)
            
            if len(images) == 0:
                result["errors"].append("No images found matching the pattern")
                return result
                
            # Check first few images for consistency
            from PIL import Image
            
            first_img_info = None
            inconsistent_sizes = []
            
            for i, img_path in enumerate(images[:5]):  # Check first 5 images
                try:
                    with Image.open(img_path) as img:
                        img_info = {
                            "size": img.size,
                            "mode": img.mode,
                            "format": img.format
                        }
                        
                        if first_img_info is None:
                            first_img_info = img_info
                            result["image_info"] = img_info
                        elif img_info["size"] != first_img_info["size"]:
                            inconsistent_sizes.append(img_path)
                            
                except Exception as e:
                    result["errors"].append(f"Cannot read image {img_path}: {str(e)}")
                    
            if inconsistent_sizes:
                result["warnings"].append(f"Inconsistent image sizes found in {len(inconsistent_sizes)} images")
                
            if len(result["errors"]) == 0:
                result["valid"] = True
                
        except Exception as e:
            result["errors"].append(f"Error validating images: {str(e)}")
            
        return result
