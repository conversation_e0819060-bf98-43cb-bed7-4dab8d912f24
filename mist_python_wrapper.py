#!/usr/bin/env python3
"""
MIST Python Wrapper
Safe wrapper for running MIST Python stitching

Author: AI Assistant
Date: 2025
"""

import os
import sys
import argparse
import logging
from typing import Dict, Optional, Callable

class MISTPythonWrapper:
    """Wrapper for MIST Python to handle imports and execution safely"""
    
    def __init__(self, mist_path: str = "mist_stitching"):
        self.mist_path = mist_path
        self.logger = logging.getLogger(__name__)
        
    def validate_mist_python(self) -> bool:
        """Check if MIST Python is available and can be imported"""
        try:
            # Check if directory exists
            if not os.path.exists(self.mist_path):
                return False
                
            # Check if main.py exists
            main_py = os.path.join(self.mist_path, "main.py")
            if not os.path.exists(main_py):
                return False
                
            # Try to add to path and import
            if self.mist_path not in sys.path:
                sys.path.insert(0, self.mist_path)
                
            # Test import without executing
            import importlib.util
            spec = importlib.util.spec_from_file_location("mist_main", main_py)
            if spec is None:
                return False
                
            return True
            
        except Exception as e:
            self.logger.error(f"Error validating MIST Python: {e}")
            return False
            
    def create_mist_args(self, config: Dict) -> argparse.Namespace:
        """Create MIST arguments from GUI configuration"""

        # Convert filename pattern
        filename_pattern = config['filename_pattern']

        # Our test images use row/col naming (tile_00_01.tif), so we should use ROWCOL
        pattern_type = 'ROWCOL'

        # Convert GUI pattern to MIST ROWCOL pattern
        # MIST expects {r} and {c} with the number of letters indicating padding
        if '{r:02d}' in filename_pattern:
            filename_pattern = filename_pattern.replace('{r:02d}', '{rr}')  # 2-digit padding
        elif '{r}' in filename_pattern:
            filename_pattern = filename_pattern.replace('{r}', '{r}')       # no padding

        if '{c:02d}' in filename_pattern:
            filename_pattern = filename_pattern.replace('{c:02d}', '{cc}')  # 2-digit padding
        elif '{c}' in filename_pattern:
            filename_pattern = filename_pattern.replace('{c}', '{c}')       # no padding
            
        # Create arguments
        args = argparse.Namespace()
        
        # Required arguments
        args.image_dirpath = config['input_directory']
        args.output_dirpath = config['output_directory']
        args.grid_width = config['grid_width']
        args.grid_height = config['grid_height']
        args.filename_pattern = filename_pattern
        args.filename_pattern_type = pattern_type
        
        # Default arguments
        args.start_tile = 0
        args.start_row = 0
        args.start_col = 0
        args.grid_origin = 'UL'  # Upper Left
        args.numbering_pattern = 'HORIZONTALCOMBING'
        args.output_prefix = 'img-'
        args.save_image = True
        args.disable_mem_cache = False
        
        # Stage model parameters - use more tolerant settings for test images
        args.stage_repeatability = config.get('stage_repeatability', 5.0)  # More tolerant
        args.horizontal_overlap = config.get('horizontal_overlap', 15.0)
        args.vertical_overlap = config.get('vertical_overlap', 15.0)
        args.overlap_uncertainty = 5.0  # More uncertainty allowed
        args.valid_correlation_threshold = 0.3  # Lower threshold for test images
        args.time_slice = 0
        
        # Advanced parameters
        args.translation_refinement_method = 'SINGLEHILLCLIMB'
        args.num_hill_climbs = 16
        args.num_fft_peaks = 2
        
        return args
        
    def run_mist_stitching(self, config: Dict, 
                          progress_callback: Optional[Callable] = None,
                          log_callback: Optional[Callable] = None) -> bool:
        """
        Run MIST Python stitching
        
        Args:
            config: Configuration dictionary
            progress_callback: Progress update function
            log_callback: Log message function
            
        Returns:
            True if successful, False otherwise
        """
        
        def log(message: str):
            self.logger.info(message)
            if log_callback:
                log_callback(message)
                
        def update_progress(value: int):
            if progress_callback:
                progress_callback(value)
                
        try:
            # Validate MIST Python
            if not self.validate_mist_python():
                raise Exception("MIST Python not found or cannot be imported")
                
            log("MIST Python validation successful")
            update_progress(10)
            
            # Create arguments
            args = self.create_mist_args(config)
            
            log(f"Input directory: {args.image_dirpath}")
            log(f"Output directory: {args.output_dirpath}")
            log(f"Grid size: {args.grid_width} x {args.grid_height}")
            log(f"Pattern: {args.filename_pattern} ({args.filename_pattern_type})")
            update_progress(20)
            
            # Change to MIST directory for imports
            original_cwd = os.getcwd()
            original_path = sys.path.copy()
            
            try:
                os.chdir(self.mist_path)
                if '.' not in sys.path:
                    sys.path.insert(0, '.')
                    
                log("Importing MIST modules...")
                update_progress(30)
                
                # Import MIST main function
                import main
                
                log("Running MIST stitching algorithm...")
                update_progress(40)
                
                # Run MIST
                main.mist(args)
                
                log("MIST stitching completed successfully!")
                update_progress(100)
                return True
                
            finally:
                # Restore original directory and path
                os.chdir(original_cwd)
                sys.path = original_path
                
        except Exception as e:
            log(f"Error during MIST stitching: {str(e)}")
            import traceback
            log(f"Traceback: {traceback.format_exc()}")
            return False

def test_mist_wrapper():
    """Test function for MIST wrapper"""
    wrapper = MISTPythonWrapper()
    
    print("Testing MIST Python wrapper...")
    
    # Test validation
    is_valid = wrapper.validate_mist_python()
    print(f"MIST Python validation: {'PASS' if is_valid else 'FAIL'}")
    
    if is_valid:
        # Test with sample config
        config = {
            'input_directory': 'test_images/small_grid',
            'output_directory': 'output_test',
            'filename_pattern': 'tile_{r:02d}_{c:02d}.tif',
            'grid_width': 3,
            'grid_height': 2,
            'horizontal_overlap': 10.0,
            'vertical_overlap': 10.0,
            'stage_repeatability': 2.0
        }
        
        print("Sample configuration created")
        args = wrapper.create_mist_args(config)
        print(f"MIST args created: {args.filename_pattern_type}")
        
    return is_valid

if __name__ == "__main__":
    test_mist_wrapper()
