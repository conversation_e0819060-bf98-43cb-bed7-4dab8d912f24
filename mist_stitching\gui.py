import tkinter as tk
from tkinter import filedialog, ttk, scrolledtext
import subprocess
import sys
import os

class MISTGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("MIST Image Stitching GUI")
        self.root.geometry("800x600")

        # Variables for inputs
        self.image_dir = tk.StringVar()
        self.output_dir = tk.StringVar()
        self.grid_width = tk.IntVar(value=1)
        self.grid_height = tk.IntVar(value=1)
        self.start_tile = tk.IntVar(value=0)
        self.start_row = tk.IntVar(value=0)
        self.start_col = tk.IntVar(value=0)
        self.filename_pattern = tk.StringVar()
        self.filename_pattern_type = tk.StringVar(value='SEQUENTIAL')
        self.grid_origin = tk.StringVar(value='UL')
        self.numbering_pattern = tk.StringVar(value='HORIZONTALCOMBING')
        self.output_prefix = tk.StringVar(value='img-')
        self.save_image = tk.BooleanVar()
        self.disable_mem_cache = tk.BooleanVar()
        self.stage_repeatability = tk.DoubleVar()
        self.horizontal_overlap = tk.DoubleVar()
        self.vertical_overlap = tk.DoubleVar()
        self.overlap_uncertainty = tk.DoubleVar(value=3.0)
        self.valid_correlation_threshold = tk.DoubleVar(value=0.5)
        self.time_slice = tk.IntVar(value=0)
        self.translation_refinement_method = tk.StringVar(value='SINGLEHILLCLIMB')
        self.num_hill_climbs = tk.IntVar(value=16)
        self.num_fft_peaks = tk.IntVar(value=2)

        self.create_widgets()

    def create_widgets(self):
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill='both', expand=True)

        # Tab 1: Basic Settings
        basic_frame = ttk.Frame(notebook)
        notebook.add(basic_frame, text='Basic')

        # Paths
        path_frame = ttk.LabelFrame(basic_frame, text="Paths")
        path_frame.pack(fill='x', padx=10, pady=5)

        tk.Label(path_frame, text="Image Directory:").grid(row=0, column=0, sticky='w')
        tk.Entry(path_frame, textvariable=self.image_dir, width=50).grid(row=0, column=1)
        tk.Button(path_frame, text="Browse", command=self.browse_image_dir).grid(row=0, column=2)

        tk.Label(path_frame, text="Output Directory:").grid(row=1, column=0, sticky='w')
        tk.Entry(path_frame, textvariable=self.output_dir, width=50).grid(row=1, column=1)
        tk.Button(path_frame, text="Browse", command=self.browse_output_dir).grid(row=1, column=2)

        # Grid
        grid_frame = ttk.LabelFrame(basic_frame, text="Grid Settings")
        grid_frame.pack(fill='x', padx=10, pady=5)

        tk.Label(grid_frame, text="Grid Width:").grid(row=0, column=0, sticky='w')
        tk.Entry(grid_frame, textvariable=self.grid_width).grid(row=0, column=1)

        tk.Label(grid_frame, text="Grid Height:").grid(row=0, column=2, sticky='w')
        tk.Entry(grid_frame, textvariable=self.grid_height).grid(row=0, column=3)

        tk.Label(grid_frame, text="Start Tile:").grid(row=1, column=0, sticky='w')
        tk.Entry(grid_frame, textvariable=self.start_tile).grid(row=1, column=1)

        tk.Label(grid_frame, text="Start Row:").grid(row=1, column=2, sticky='w')
        tk.Entry(grid_frame, textvariable=self.start_row).grid(row=1, column=3)

        tk.Label(grid_frame, text="Start Col:").grid(row=1, column=4, sticky='w')
        tk.Entry(grid_frame, textvariable=self.start_col).grid(row=1, column=5)

        # Filename
        filename_frame = ttk.LabelFrame(basic_frame, text="Filename Settings")
        filename_frame.pack(fill='x', padx=10, pady=5)

        tk.Label(filename_frame, text="Filename Pattern:").grid(row=0, column=0, sticky='w')
        tk.Entry(filename_frame, textvariable=self.filename_pattern, width=30).grid(row=0, column=1)

        tk.Label(filename_frame, text="Pattern Type:").grid(row=0, column=2, sticky='w')
        ttk.Combobox(filename_frame, textvariable=self.filename_pattern_type, values=['SEQUENTIAL', 'ROWCOL']).grid(row=0, column=3)

        tk.Label(filename_frame, text="Grid Origin:").grid(row=1, column=0, sticky='w')
        ttk.Combobox(filename_frame, textvariable=self.grid_origin, values=['UL', 'UR', 'LL', 'LR']).grid(row=1, column=1)

        tk.Label(filename_frame, text="Numbering Pattern:").grid(row=1, column=2, sticky='w')
        ttk.Combobox(filename_frame, textvariable=self.numbering_pattern, values=['HORIZONTALCOMBING', 'VERTICALCOMBING', 'HORIZONTALCONTINUOUS', 'VERTICALCONTINUOUS']).grid(row=1, column=3)

        tk.Label(filename_frame, text="Output Prefix:").grid(row=2, column=0, sticky='w')
        tk.Entry(filename_frame, textvariable=self.output_prefix).grid(row=2, column=1)

        tk.Label(filename_frame, text="Time Slice:").grid(row=2, column=2, sticky='w')
        tk.Entry(filename_frame, textvariable=self.time_slice).grid(row=2, column=3)

        # Options
        options_frame = ttk.LabelFrame(basic_frame, text="Options")
        options_frame.pack(fill='x', padx=10, pady=5)

        tk.Checkbutton(options_frame, text="Save Image", variable=self.save_image).pack(side='left')
        tk.Checkbutton(options_frame, text="Disable Mem Cache", variable=self.disable_mem_cache).pack(side='left')

        # Tab 2: Advanced Settings
        advanced_frame = ttk.Frame(notebook)
        notebook.add(advanced_frame, text='Advanced')

        # Stage Model
        stage_frame = ttk.LabelFrame(advanced_frame, text="Stage Model")
        stage_frame.pack(fill='x', padx=10, pady=5)

        tk.Label(stage_frame, text="Stage Repeatability:").grid(row=0, column=0, sticky='w')
        tk.Entry(stage_frame, textvariable=self.stage_repeatability).grid(row=0, column=1)

        tk.Label(stage_frame, text="Horizontal Overlap:").grid(row=0, column=2, sticky='w')
        tk.Entry(stage_frame, textvariable=self.horizontal_overlap).grid(row=0, column=3)

        tk.Label(stage_frame, text="Vertical Overlap:").grid(row=1, column=0, sticky='w')
        tk.Entry(stage_frame, textvariable=self.vertical_overlap).grid(row=1, column=1)

        tk.Label(stage_frame, text="Overlap Uncertainty:").grid(row=1, column=2, sticky='w')
        tk.Entry(stage_frame, textvariable=self.overlap_uncertainty).grid(row=1, column=3)

        tk.Label(stage_frame, text="Valid Correlation Threshold:").grid(row=2, column=0, sticky='w')
        tk.Entry(stage_frame, textvariable=self.valid_correlation_threshold).grid(row=2, column=1)

        # Refinement
        refine_frame = ttk.LabelFrame(advanced_frame, text="Refinement")
        refine_frame.pack(fill='x', padx=10, pady=5)

        tk.Label(refine_frame, text="Translation Refinement Method:").grid(row=0, column=0, sticky='w')
        ttk.Combobox(refine_frame, textvariable=self.translation_refinement_method, values=['SINGLEHILLCLIMB', 'MULTIPOINTHILLCLIMB']).grid(row=0, column=1)

        tk.Label(refine_frame, text="Num Hill Climbs:").grid(row=0, column=2, sticky='w')
        tk.Entry(refine_frame, textvariable=self.num_hill_climbs).grid(row=0, column=3)

        tk.Label(refine_frame, text="Num FFT Peaks:").grid(row=1, column=0, sticky='w')
        tk.Entry(refine_frame, textvariable=self.num_fft_peaks).grid(row=1, column=1)

        # Run Button and Output
        run_frame = ttk.Frame(self.root)
        run_frame.pack(fill='x', padx=10, pady=5)

        tk.Button(run_frame, text="Run MIST", command=self.run_mist).pack(side='left')
        tk.Button(run_frame, text="Clear Output", command=self.clear_output).pack(side='left')

        self.output_text = scrolledtext.ScrolledText(self.root, height=10)
        self.output_text.pack(fill='both', expand=True, padx=10, pady=5)

    def browse_image_dir(self):
        dir_path = filedialog.askdirectory()
        if dir_path:
            self.image_dir.set(dir_path)

    def browse_output_dir(self):
        dir_path = filedialog.askdirectory()
        if dir_path:
            self.output_dir.set(dir_path)

    def run_mist(self):
        # Build command
        cmd = [sys.executable, 'main.py']

        # Required
        if not self.image_dir.get():
            self.output_text.insert(tk.END, "Error: Image directory is required\n")
            return
        cmd.extend(['--image-dirpath', self.image_dir.get()])

        if not self.output_dir.get():
            self.output_text.insert(tk.END, "Error: Output directory is required\n")
            return
        cmd.extend(['--output-dirpath', self.output_dir.get()])

        cmd.extend(['--grid-width', str(self.grid_width.get())])
        cmd.extend(['--grid-height', str(self.grid_height.get())])

        if not self.filename_pattern.get():
            self.output_text.insert(tk.END, "Error: Filename pattern is required\n")
            return
        cmd.extend(['--filename-pattern', self.filename_pattern.get()])
        cmd.extend(['--filename-pattern-type', self.filename_pattern_type.get()])
        cmd.extend(['--grid-origin', self.grid_origin.get()])
        cmd.extend(['--numbering-pattern', self.numbering_pattern.get()])

        # Optional
        if self.start_tile.get() != 0:
            cmd.extend(['--start-tile', str(self.start_tile.get())])
        if self.start_row.get() != 0:
            cmd.extend(['--start-row', str(self.start_row.get())])
        if self.start_col.get() != 0:
            cmd.extend(['--start-col', str(self.start_col.get())])
        if self.output_prefix.get() != 'img-':
            cmd.extend(['--output-prefix', self.output_prefix.get()])
        if self.save_image.get():
            cmd.append('--save-image')
        if self.disable_mem_cache.get():
            cmd.append('--disable-mem-cache')
        if self.stage_repeatability.get():
            cmd.extend(['--stage-repeatability', str(self.stage_repeatability.get())])
        if self.horizontal_overlap.get():
            cmd.extend(['--horizontal-overlap', str(self.horizontal_overlap.get())])
        if self.vertical_overlap.get():
            cmd.extend(['--vertical-overlap', str(self.vertical_overlap.get())])
        if self.overlap_uncertainty.get() != 3.0:
            cmd.extend(['--overlap-uncertainty', str(self.overlap_uncertainty.get())])
        if self.valid_correlation_threshold.get() != 0.5:
            cmd.extend(['--valid-correlation-threshold', str(self.valid_correlation_threshold.get())])
        if self.time_slice.get() != 0:
            cmd.extend(['--time-slice', str(self.time_slice.get())])
        if self.translation_refinement_method.get() != 'SINGLEHILLCLIMB':
            cmd.extend(['--translation-refinement-method', self.translation_refinement_method.get()])
        if self.num_hill_climbs.get() != 16:
            cmd.extend(['--num-hill-climbs', str(self.num_hill_climbs.get())])
        if self.num_fft_peaks.get() != 2:
            cmd.extend(['--num-fft-peaks', str(self.num_fft_peaks.get())])

        self.output_text.insert(tk.END, f"Running command: {' '.join(cmd)}\n")
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, cwd=os.path.dirname(__file__))
            self.output_text.insert(tk.END, result.stdout)
            if result.stderr:
                self.output_text.insert(tk.END, "STDERR:\n" + result.stderr)
        except Exception as e:
            self.output_text.insert(tk.END, f"Error running MIST: {str(e)}\n")

    def clear_output(self):
        self.output_text.delete(1.0, tk.END)

if __name__ == "__main__":
    root = tk.Tk()
    app = MISTGUI(root)
    root.mainloop()