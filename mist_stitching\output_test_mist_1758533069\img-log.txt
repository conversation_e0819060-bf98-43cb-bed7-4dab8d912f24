2025-09-22 16:24:32,473 [INFO] [img_grid.py:85] Tile grid:
None	None	None	
None	None	None	
2025-09-22 16:24:32,473 [INFO] [main.py:53] Computing all pairwise translations for between images
2025-09-22 16:24:32,474 [INFO] [pciam.py:230] Computing all pairwise translations in parallel
2025-09-22 16:24:32,474 [INFO] [pciam.py:231] Preloading all images into memory
2025-09-22 16:24:32,474 [INFO] [pciam.py:233] Finished preloading all images into memory. Took 0.0s
2025-09-22 16:24:32,474 [INFO] [utils.py:26] Using 8 Workers
2025-09-22 16:24:32,557 [INFO] [pciam.py:269] Finished computing all pairwise translations in 0.08217191696166992 seconds
2025-09-22 16:24:32,559 [INFO] [stage_model.py:59] Grid translations for direction: VERTICAL
2025-09-22 16:24:32,560 [INFO] [mist_python_wrapper.py:124] Error during MIST stitching: No translations found in direction: VERTICAL
2025-09-22 16:24:32,561 [INFO] [mist_python_wrapper.py:124] Traceback: Traceback (most recent call last):
  File "D:\Downloads\MIST-python\MIST-python\src\mist_python_wrapper.py", line 168, in run_mist_stitching
    main.mist(args)
  File "D:\Downloads\MIST-python\MIST-python\src\mist_stitching\.\main.py", line 71, in mist
    sm.build()
  File "D:\Downloads\MIST-python\MIST-python\src\mist_stitching\.\stage_model.py", line 491, in build
    self.vertical_overlap = self.compute_overlap("VERTICAL")
  File "D:\Downloads\MIST-python\MIST-python\src\mist_stitching\.\stage_model.py", line 70, in compute_overlap
    raise RuntimeError("No translations found in direction: {}".format(direction))
RuntimeError: No translations found in direction: VERTICAL

2025-09-22 16:24:32,561 [INFO] [mist_integration.py:159] MIST stitching failed
