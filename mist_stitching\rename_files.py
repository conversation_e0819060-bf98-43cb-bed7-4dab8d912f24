import os
import re
import tkinter as tk
from tkinter import filedialog, scrolledtext
import shutil

class RenameGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("MIST File Renamer")
        self.root.geometry("600x400")

        self.folder_path = tk.StringVar()
        self.height = tk.IntVar(value=4)
        self.width = tk.IntVar(value=9)
        self.order_str = tk.StringVar(value="27,28,29,30,31,32,33,34,35,18,19,20,21,22,23,24,25,26,9,10,11,12,13,14,15,16,17,0,1,2,3,4,5,6,7,8")
        self.sequential = tk.BooleanVar(value=False)

        # Widgets
        tk.Label(root, text="Folder Path:").grid(row=0, column=0, sticky='w')
        tk.Entry(root, textvariable=self.folder_path, width=50).grid(row=0, column=1)
        tk.Button(root, text="Browse", command=self.browse_folder).grid(row=0, column=2)

        tk.Label(root, text="Grid Height:").grid(row=1, column=0, sticky='w')
        tk.Entry(root, textvariable=self.height).grid(row=1, column=1)

        tk.Label(root, text="Grid Width:").grid(row=1, column=2, sticky='w')
        tk.Entry(root, textvariable=self.width).grid(row=1, column=3)

        tk.Label(root, text="Order (comma separated):").grid(row=2, column=0, sticky='w')
        tk.Entry(root, textvariable=self.order_str, width=50).grid(row=2, column=1, columnspan=3)

        tk.Checkbutton(root, text="Rename sequentially (ignore order)", variable=self.sequential).grid(row=3, column=0, columnspan=4)

        tk.Button(root, text="Scan Files", command=self.scan_files).grid(row=2, column=0, pady=10)
        tk.Button(root, text="Rename Files", command=self.rename_files).grid(row=2, column=1, pady=10)

        self.text_area = scrolledtext.ScrolledText(root, height=15)
        self.text_area.grid(row=3, column=0, columnspan=4, sticky='nsew')

    def browse_folder(self):
        folder = filedialog.askdirectory()
        if folder:
            self.folder_path.set(folder)

    def scan_files(self):
        folder = self.folder_path.get()
        if not folder or not os.path.exists(folder):
            self.text_area.insert(tk.END, "Invalid folder path\n")
            return

        pattern = re.compile(r'.*p(\d{3}).*\.png')
        files = []
        for f in os.listdir(folder):
            if f.endswith('.png'):
                match = pattern.search(f)
                if match:
                    num = int(match.group(1))
                    files.append((num, f))

        files.sort(key=lambda x: x[0])
        self.text_area.insert(tk.END, f"Found {len(files)} files:\n")
        for num, name in files:
            self.text_area.insert(tk.END, f"{name} (p{num:03d})\n")

        self.files = files

    def rename_files(self):
        if not hasattr(self, 'files'):
            self.text_area.insert(tk.END, "Please scan files first\n")
            return

        folder = self.folder_path.get()
        expected = self.height.get() * self.width.get()

        if self.sequential.get():
            # Rename sequentially
            if len(self.files) != expected:
                self.text_area.insert(tk.END, f"Number of files ({len(self.files)}) does not match grid size ({expected})\n")
                return
            renamed_count = 0
            for i, (num, old_name) in enumerate(self.files):
                new_name = f"tile_p{i:03d}.png"
                old_path = os.path.join(folder, old_name)
                new_path = os.path.join(folder, new_name)
                shutil.move(old_path, new_path)
                self.text_area.insert(tk.END, f"Renamed {old_name} to {new_name}\n")
                renamed_count += 1
            self.text_area.insert(tk.END, f"Renaming completed: {renamed_count} files renamed\n")
        else:
            # Use order
            try:
                order = [int(x.strip()) for x in self.order_str.get().split(',')]
            except ValueError:
                self.text_area.insert(tk.END, "Invalid order format. Use comma separated numbers.\n")
                return

            if len(order) != expected:
                self.text_area.insert(tk.END, f"Order length ({len(order)}) does not match grid size ({expected})\n")
                return

            # Create a dict num -> filename
            file_dict = {num: name for num, name in self.files}

            renamed_count = 0
            for i, num in enumerate(order):
                if num not in file_dict:
                    self.text_area.insert(tk.END, f"Missing file for p{num:03d}\n")
                    continue

                old_name = file_dict[num]
                new_name = f"tile_p{i:03d}.png"
                old_path = os.path.join(folder, old_name)
                new_path = os.path.join(folder, new_name)

                shutil.move(old_path, new_path)
                self.text_area.insert(tk.END, f"Renamed {old_name} to {new_name}\n")
                renamed_count += 1

            self.text_area.insert(tk.END, f"Renaming completed: {renamed_count} files renamed\n")

if __name__ == "__main__":
    root = tk.Tk()
    app = RenameGUI(root)
    root.mainloop()