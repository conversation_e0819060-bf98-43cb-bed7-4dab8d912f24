#!/usr/bin/env python3


import os
import json
import numpy as np
from PIL import Image, ImageTk
import tkinter as tk
from typing import Dict, List, Tuple, Optional
import math

class ImagePreviewGenerator:
    """Generate preview images for grid visualization"""
    
    @staticmethod
    def create_grid_preview(input_dir: str, pattern: str, grid_width: int, grid_height: int,
                           preview_size: Tuple[int, int] = (400, 300)) -> Optional[Image.Image]:
        """
        Create a preview image showing the grid layout
        
        Args:
            input_dir: Input directory path
            pattern: Filename pattern
            grid_width: Number of columns
            grid_height: Number of rows
            preview_size: Size of preview image
            
        Returns:
            PIL Image or None if failed
        """
        try:
            from mist_integration import MISTIntegration
            
            integration = MISTIntegration()
            images = integration.get_input_images(input_dir, pattern)
            
            if len(images) == 0:
                return None
                
            # Calculate cell size for preview
            cell_width = preview_size[0] // grid_width
            cell_height = preview_size[1] // grid_height
            
            # Create preview image
            preview = Image.new('RGB', preview_size, 'white')
            
            # Load and place images
            for i, img_path in enumerate(images[:grid_width * grid_height]):
                row = i // grid_width
                col = i % grid_width
                
                try:
                    # Load and resize image
                    with Image.open(img_path) as img:
                        img_resized = img.resize((cell_width, cell_height), Image.Resampling.LANCZOS)
                        
                        # Convert to RGB if necessary
                        if img_resized.mode != 'RGB':
                            img_resized = img_resized.convert('RGB')
                            
                        # Paste into preview
                        x = col * cell_width
                        y = row * cell_height
                        preview.paste(img_resized, (x, y))
                        
                except Exception:
                    # If image can't be loaded, draw a placeholder
                    from PIL import ImageDraw
                    draw = ImageDraw.Draw(preview)
                    x1 = col * cell_width
                    y1 = row * cell_height
                    x2 = x1 + cell_width
                    y2 = y1 + cell_height
                    
                    draw.rectangle([x1, y1, x2, y2], outline='red', width=2)
                    draw.text((x1 + 5, y1 + 5), f"Error\n{i+1}", fill='red')
                    
            return preview
            
        except Exception as e:
            print(f"Error creating preview: {e}")
            return None
            
    @staticmethod
    def create_overlap_visualization(grid_width: int, grid_height: int, 
                                   h_overlap: float, v_overlap: float,
                                   canvas_size: Tuple[int, int] = (400, 300)) -> Image.Image:
        """
        Create visualization showing overlap regions
        
        Args:
            grid_width: Number of columns
            grid_height: Number of rows
            h_overlap: Horizontal overlap percentage
            v_overlap: Vertical overlap percentage
            canvas_size: Size of canvas
            
        Returns:
            PIL Image showing overlap visualization
        """
        from PIL import ImageDraw
        
        # Create image
        img = Image.new('RGB', canvas_size, 'white')
        draw = ImageDraw.Draw(img)
        
        # Calculate cell dimensions
        margin = 20
        available_width = canvas_size[0] - 2 * margin
        available_height = canvas_size[1] - 2 * margin
        
        cell_width = available_width / grid_width
        cell_height = available_height / grid_height
        
        # Calculate overlap in pixels
        h_overlap_px = cell_width * (h_overlap / 100)
        v_overlap_px = cell_height * (v_overlap / 100)
        
        # Draw grid with overlaps
        for row in range(grid_height):
            for col in range(grid_width):
                x1 = margin + col * (cell_width - h_overlap_px)
                y1 = margin + row * (cell_height - v_overlap_px)
                x2 = x1 + cell_width
                y2 = y1 + cell_height
                
                # Draw cell
                draw.rectangle([x1, y1, x2, y2], outline='blue', width=1, fill='lightblue')
                
                # Draw overlap regions
                if col > 0:  # Horizontal overlap
                    overlap_x1 = x1
                    overlap_x2 = x1 + h_overlap_px
                    draw.rectangle([overlap_x1, y1, overlap_x2, y2], 
                                 outline='red', width=1, fill='lightcoral')
                    
                if row > 0:  # Vertical overlap
                    overlap_y1 = y1
                    overlap_y2 = y1 + v_overlap_px
                    draw.rectangle([x1, overlap_y1, x2, overlap_y2], 
                                 outline='green', width=1, fill='lightgreen')
                
                # Draw position number
                pos = row * grid_width + col + 1
                text_x = x1 + cell_width / 2
                text_y = y1 + cell_height / 2
                draw.text((text_x, text_y), str(pos), fill='black', anchor='mm')
                
        # Add legend
        legend_y = canvas_size[1] - 15
        draw.rectangle([10, legend_y - 10, 20, legend_y], fill='lightcoral')
        draw.text((25, legend_y - 5), "H-Overlap", fill='black')
        
        draw.rectangle([100, legend_y - 10, 110, legend_y], fill='lightgreen')
        draw.text((115, legend_y - 5), "V-Overlap", fill='black')
        
        return img

class ConfigManager:
    """Manage MIST configuration files"""
    
    @staticmethod
    def get_default_config() -> Dict:
        """Get default MIST configuration"""
        return {
            "input_directory": "",
            "output_directory": "",
            "filename_pattern": "img_{r:02d}_{c:02d}.tif",
            "grid_width": 10,
            "grid_height": 10,
            "horizontal_overlap": 10.0,
            "vertical_overlap": 10.0,
            "stage_repeatability": 2.0,
            "blend_method": "LINEAR",
            "output_format": "TIF",
            "use_gpu": True,
            "save_metadata": True,
            "generate_preview": True
        }
        
    @staticmethod
    def validate_config_file(filepath: str) -> Tuple[bool, List[str]]:
        """
        Validate a configuration file
        
        Args:
            filepath: Path to config file
            
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        errors = []
        
        try:
            with open(filepath, 'r') as f:
                config = json.load(f)
                
            # Check required fields
            default_config = ConfigManager.get_default_config()
            for key in default_config:
                if key not in config:
                    errors.append(f"Missing required field: {key}")
                    
            # Type validation
            if 'grid_width' in config and not isinstance(config['grid_width'], int):
                errors.append("grid_width must be an integer")
                
            if 'grid_height' in config and not isinstance(config['grid_height'], int):
                errors.append("grid_height must be an integer")
                
            # Range validation
            for overlap_field in ['horizontal_overlap', 'vertical_overlap']:
                if overlap_field in config:
                    value = config[overlap_field]
                    if not isinstance(value, (int, float)) or not (0 <= value <= 50):
                        errors.append(f"{overlap_field} must be between 0 and 50")
                        
        except json.JSONDecodeError as e:
            errors.append(f"Invalid JSON format: {str(e)}")
        except FileNotFoundError:
            errors.append("Configuration file not found")
        except Exception as e:
            errors.append(f"Error reading configuration: {str(e)}")
            
        return len(errors) == 0, errors

class FilenamePatternHelper:
    """Helper for filename pattern generation and validation"""
    
    @staticmethod
    def generate_pattern_examples(pattern: str, grid_width: int, grid_height: int) -> List[str]:
        """
        Generate example filenames based on pattern
        
        Args:
            pattern: Filename pattern with placeholders
            grid_width: Number of columns
            grid_height: Number of rows
            
        Returns:
            List of example filenames
        """
        examples = []
        
        try:
            # Generate a few examples
            positions = [
                (0, 0),  # Top-left
                (0, grid_width - 1),  # Top-right
                (grid_height - 1, 0),  # Bottom-left
                (grid_height - 1, grid_width - 1)  # Bottom-right
            ]
            
            for row, col in positions:
                pos = row * grid_width + col + 1
                
                # Replace placeholders
                filename = pattern
                filename = filename.replace('{r}', str(row))
                filename = filename.replace('{c}', str(col))
                filename = filename.replace('{p}', str(pos))
                
                # Handle formatted placeholders
                import re
                filename = re.sub(r'\{r:(\d+)d\}', lambda m: f"{row:0{m.group(1)}d}", filename)
                filename = re.sub(r'\{c:(\d+)d\}', lambda m: f"{col:0{m.group(1)}d}", filename)
                filename = re.sub(r'\{p:(\d+)d\}', lambda m: f"{pos:0{m.group(1)}d}", filename)
                
                examples.append(filename)
                
        except Exception:
            examples = ["Error in pattern"]
            
        return examples
        
    @staticmethod
    def validate_pattern(pattern: str) -> Tuple[bool, str]:
        """
        Validate filename pattern
        
        Args:
            pattern: Filename pattern to validate
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        if not pattern:
            return False, "Pattern cannot be empty"
            
        # Check for required placeholders
        has_position_info = any(placeholder in pattern for placeholder in ['{r}', '{c}', '{p}'])
        
        if not has_position_info:
            return False, "Pattern must contain at least one position placeholder ({r}, {c}, or {p})"
            
        # Check for file extension
        if '.' not in pattern:
            return False, "Pattern should include file extension"
            
        # Check for invalid characters
        invalid_chars = ['<', '>', ':', '"', '|', '?', '*']
        for char in invalid_chars:
            if char in pattern:
                return False, f"Pattern contains invalid character: {char}"
                
        return True, ""

class ProgressTracker:
    """Track and estimate progress for long-running operations"""
    
    def __init__(self, total_steps: int = 100):
        self.total_steps = total_steps
        self.current_step = 0
        self.step_times = []
        self.start_time = None
        
    def start(self):
        """Start progress tracking"""
        import time
        self.start_time = time.time()
        self.current_step = 0
        self.step_times = []
        
    def update(self, step: int, message: str = ""):
        """Update progress"""
        import time
        current_time = time.time()
        
        if self.start_time:
            elapsed = current_time - self.start_time
            self.step_times.append(elapsed)
            
        self.current_step = step
        
    def get_progress_percent(self) -> float:
        """Get current progress as percentage"""
        return (self.current_step / self.total_steps) * 100
        
    def estimate_remaining_time(self) -> float:
        """Estimate remaining time in seconds"""
        if len(self.step_times) < 2:
            return 0.0
            
        # Calculate average time per step
        avg_time_per_step = sum(self.step_times) / len(self.step_times)
        remaining_steps = self.total_steps - self.current_step
        
        return remaining_steps * avg_time_per_step
        
    def format_time(self, seconds: float) -> str:
        """Format time in human-readable format"""
        if seconds < 60:
            return f"{seconds:.0f} seconds"
        elif seconds < 3600:
            minutes = seconds / 60
            return f"{minutes:.1f} minutes"
        else:
            hours = seconds / 3600
            return f"{hours:.1f} hours"

class ImageAnalyzer:
    """Analyze images for stitching compatibility"""
    
    @staticmethod
    def analyze_image_set(image_paths: List[str]) -> Dict:
        """
        Analyze a set of images for stitching compatibility
        
        Args:
            image_paths: List of image file paths
            
        Returns:
            Dictionary with analysis results
        """
        analysis = {
            "total_images": len(image_paths),
            "valid_images": 0,
            "image_sizes": [],
            "image_formats": [],
            "consistent_size": True,
            "consistent_format": True,
            "total_size_mb": 0,
            "errors": []
        }
        
        if not image_paths:
            analysis["errors"].append("No images to analyze")
            return analysis
            
        reference_size = None
        reference_format = None
        
        for img_path in image_paths:
            try:
                with Image.open(img_path) as img:
                    size = img.size
                    format_type = img.format
                    
                    analysis["image_sizes"].append(size)
                    analysis["image_formats"].append(format_type)
                    analysis["valid_images"] += 1
                    
                    # Check file size
                    file_size = os.path.getsize(img_path) / (1024 * 1024)  # MB
                    analysis["total_size_mb"] += file_size
                    
                    # Check consistency
                    if reference_size is None:
                        reference_size = size
                        reference_format = format_type
                    else:
                        if size != reference_size:
                            analysis["consistent_size"] = False
                        if format_type != reference_format:
                            analysis["consistent_format"] = False
                            
            except Exception as e:
                analysis["errors"].append(f"Error reading {img_path}: {str(e)}")
                
        # Calculate statistics
        if analysis["image_sizes"]:
            unique_sizes = list(set(analysis["image_sizes"]))
            analysis["unique_sizes"] = unique_sizes
            analysis["most_common_size"] = max(set(analysis["image_sizes"]), 
                                             key=analysis["image_sizes"].count)
                                             
        if analysis["image_formats"]:
            unique_formats = list(set(analysis["image_formats"]))
            analysis["unique_formats"] = unique_formats
            analysis["most_common_format"] = max(set(analysis["image_formats"]), 
                                               key=analysis["image_formats"].count)
                                               
        return analysis
