"""
grid_stitcher_ui.py
Simple PyQt5 GUI for grid-based stitching with linear feather blending.

Assumptions:
- Tiles are named/sorted in reading order (left-to-right, top-to-bottom).
- All tiles are same size. If not, the script will attempt to resize to the size of the first tile.
- You provide rows and cols values that match the total number of selected images.

Dependencies:
- PyQt5
- OpenCV (cv2)
- numpy
"""

import sys
import os
import math
from functools import partial

import numpy as np
import cv2
from PyQt5 import QtWidgets, QtGui, QtCore


def natural_sort_key(s):
    # basic natural sort for filenames
    import re
    return [int(text) if text.isdigit() else text.lower()
            for text in re.split('([0-9]+)', s)]


def linear_feather_mask(h, w, overlap_x_px, overlap_y_px):
    """
    Create a 2D weight mask for a tile where edges overlap fade linearly.
    center region weight = 1. Edges within overlap range ramp to 0.
    """
    # horizontal ramp
    x = np.ones((w,), dtype=np.float32)
    if overlap_x_px > 0:
        ramp = np.linspace(0, 1, overlap_x_px, endpoint=False, dtype=np.float32)
        left = ramp.copy()
        right = ramp[::-1].copy()
        mid_len = w - 2 * overlap_x_px
        if mid_len < 0:
            # overlap greater than tile dimension -> create triangular weights
            ramp_full = np.linspace(0, 1, w // 2 + 1, dtype=np.float32)
            x[:w // 2 + 1] = ramp_full
            x[w // 2 + 1:] = ramp_full[::-1][:w - (w // 2 + 1)]
        else:
            x[:overlap_x_px] = left
            x[-overlap_x_px:] = right
    # vertical ramp
    y = np.ones((h,), dtype=np.float32)
    if overlap_y_px > 0:
        ramp = np.linspace(0, 1, overlap_y_px, endpoint=False, dtype=np.float32)
        top = ramp.copy()
        bottom = ramp[::-1].copy()
        mid_len = h - 2 * overlap_y_px
        if mid_len < 0:
            ramp_full = np.linspace(0, 1, h // 2 + 1, dtype=np.float32)
            y[:h // 2 + 1] = ramp_full
            y[h // 2 + 1:] = ramp_full[::-1][:h - (h // 2 + 1)]
        else:
            y[:overlap_y_px] = top
            y[-overlap_y_px:] = bottom
    mask = np.outer(y, x)  # h x w
    return mask


def stitch_grid_linear_blend(image_paths, rows, cols, overlap_x, overlap_y,
                             preview_scale=0.25, status_cb=None,
                             local_exposure_comp=True):
    """
    Grid stitch with optional local overlap exposure compensation.
    """
    assert len(image_paths) == rows * cols, "number of images != rows * cols"

    # load images
    imgs = []
    for p in image_paths:
        img = cv2.imread(p, cv2.IMREAD_COLOR)
        if img is None:
            raise IOError(f"failed to read: {p}")
        imgs.append(img)

    base_h, base_w = imgs[0].shape[:2]
    for i in range(len(imgs)):
        h, w = imgs[i].shape[:2]
        if h != base_h or w != base_w:
            imgs[i] = cv2.resize(imgs[i], (base_w, base_h), interpolation=cv2.INTER_LINEAR)

    h, w = base_h, base_w
    step_x = int(round(w * (1 - overlap_x)))
    step_y = int(round(h * (1 - overlap_y)))
    canvas_w = step_x * (cols - 1) + w
    canvas_h = step_y * (rows - 1) + h

    acc = np.zeros((canvas_h, canvas_w, 3), dtype=np.float32)
    weight_sum = np.zeros((canvas_h, canvas_w), dtype=np.float32)

    overlap_x_px = int(round(w * overlap_x))
    overlap_y_px = int(round(h * overlap_y))
    tile_mask = linear_feather_mask(h, w, overlap_x_px, overlap_y_px)

    # local exposure compensation
    if local_exposure_comp:
        for r in range(rows):
            for c in range(cols):
                idx = r * cols + c
                tile = imgs[idx].astype(np.float32)

                # horizontal overlap (with left neighbor)
                if c > 0 and overlap_x_px > 0:
                    left_idx = r * cols + (c - 1)
                    left_tile = imgs[left_idx].astype(np.float32)
                    # overlap region
                    x0 = w - overlap_x_px
                    x1 = w
                    roi1 = left_tile[:, x0:x1, :]
                    roi2 = tile[:, :overlap_x_px, :]
                    mean1 = np.mean(cv2.cvtColor(roi1.astype(np.uint8), cv2.COLOR_BGR2GRAY))
                    mean2 = np.mean(cv2.cvtColor(roi2.astype(np.uint8), cv2.COLOR_BGR2GRAY))
                    gain = (mean1 + 1e-6) / (mean2 + 1e-6)
                    tile[:, :overlap_x_px, :] *= gain

                # vertical overlap (with top neighbor)
                if r > 0 and overlap_y_px > 0:
                    top_idx = (r - 1) * cols + c
                    top_tile = imgs[top_idx].astype(np.float32)
                    # overlap region
                    y0 = h - overlap_y_px
                    y1 = h
                    roi1 = top_tile[y0:y1, :, :]
                    roi2 = tile[:overlap_y_px, :, :]
                    mean1 = np.mean(cv2.cvtColor(roi1.astype(np.uint8), cv2.COLOR_BGR2GRAY))
                    mean2 = np.mean(cv2.cvtColor(roi2.astype(np.uint8), cv2.COLOR_BGR2GRAY))
                    gain = (mean1 + 1e-6) / (mean2 + 1e-6)
                    tile[:overlap_y_px, :, :] *= gain

                imgs[idx] = np.clip(tile, 0, 255).astype(np.uint8)

    # place tiles
    idx = 0
    for r in range(rows):
        for c in range(cols):
            y = r * step_y
            x = c * step_x
            tile = imgs[idx].astype(np.float32)
            for ch in range(3):
                acc[y:y+h, x:x+w, ch] += tile[:, :, ch] * tile_mask
            weight_sum[y:y+h, x:x+w] += tile_mask
            idx += 1

    mask_nonzero = weight_sum > 1e-6
    result = np.zeros_like(acc, dtype=np.uint8)
    for ch in range(3):
        channel = acc[:, :, ch]
        channel_out = np.zeros_like(channel, dtype=np.float32)
        channel_out[mask_nonzero] = channel[mask_nonzero] / weight_sum[mask_nonzero]
        result[:, :, ch] = np.clip(channel_out, 0, 255).astype(np.uint8)

    preview = None
    if preview_scale and 0 < preview_scale < 1.0:
        ph = max(1, int(canvas_h * preview_scale))
        pw = max(1, int(canvas_w * preview_scale))
        preview = cv2.resize(result, (pw, ph), interpolation=cv2.INTER_AREA)

    return result, preview



class GridStitcherApp(QtWidgets.QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Grid Stitcher (linear feather blending)")
        self.resize(1000, 700)
        self._setup_ui()
        self.image_paths = []

    def _setup_ui(self):
        central = QtWidgets.QWidget()
        self.setCentralWidget(central)
        layout = QtWidgets.QVBoxLayout(central)

        # top controls
        top_row = QtWidgets.QHBoxLayout()
        layout.addLayout(top_row)

        self.btn_load = QtWidgets.QPushButton("Load Images")
        self.btn_load.clicked.connect(self.on_load)
        top_row.addWidget(self.btn_load)

        top_row.addWidget(QtWidgets.QLabel("Rows:"))
        self.spin_rows = QtWidgets.QSpinBox(); self.spin_rows.setMinimum(1); self.spin_rows.setValue(4)
        top_row.addWidget(self.spin_rows)

        top_row.addWidget(QtWidgets.QLabel("Cols:"))
        self.spin_cols = QtWidgets.QSpinBox(); self.spin_cols.setMinimum(1); self.spin_cols.setValue(9)
        top_row.addWidget(self.spin_cols)

        top_row.addWidget(QtWidgets.QLabel("Overlap X %:"))
        self.spin_ox = QtWidgets.QDoubleSpinBox(); self.spin_ox.setDecimals(2); self.spin_ox.setRange(0.0, 0.9); self.spin_ox.setSingleStep(0.05); self.spin_ox.setValue(0.15)
        top_row.addWidget(self.spin_ox)

        top_row.addWidget(QtWidgets.QLabel("Overlap Y %:"))
        self.spin_oy = QtWidgets.QDoubleSpinBox(); self.spin_oy.setDecimals(2); self.spin_oy.setRange(0.0, 0.9); self.spin_oy.setSingleStep(0.05); self.spin_oy.setValue(0.15)
        top_row.addWidget(self.spin_oy)

        self.btn_preview = QtWidgets.QPushButton("Preview Stitch")
        self.btn_preview.clicked.connect(self.on_preview)
        top_row.addWidget(self.btn_preview)

        self.btn_stitch = QtWidgets.QPushButton("Stitch & Save")
        self.btn_stitch.clicked.connect(self.on_stitch)
        top_row.addWidget(self.btn_stitch)

        # image list on left + preview on right
        main_h = QtWidgets.QHBoxLayout()
        layout.addLayout(main_h, stretch=1)

        self.list_widget = QtWidgets.QListWidget()
        self.list_widget.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        main_h.addWidget(self.list_widget, stretch=1)

        right_v = QtWidgets.QVBoxLayout()
        main_h.addLayout(right_v, stretch=3)

        self.label_preview = QtWidgets.QLabel("Preview will appear here")
        self.label_preview.setAlignment(QtCore.Qt.AlignCenter)
        self.label_preview.setMinimumSize(400, 300)
        self.label_preview.setStyleSheet("QLabel { background: #222; color: #ddd; }")
        right_v.addWidget(self.label_preview, stretch=1)

        info_h = QtWidgets.QHBoxLayout()
        right_v.addLayout(info_h)
        self.lbl_info = QtWidgets.QLabel("No images loaded")
        info_h.addWidget(self.lbl_info)
        info_h.addStretch()

        # status bar
        self.status = QtWidgets.QLabel("")
        layout.addWidget(self.status)

    def log_status(self, text):
        self.status.setText(str(text))
        QtWidgets.QApplication.processEvents()

    def on_load(self):
        # allow selecting multiple images
        files, _ = QtWidgets.QFileDialog.getOpenFileNames(self, "Select tile images", os.getcwd(),
                                                          "Images (*.png *.jpg *.tif *.tiff *.bmp);;All files (*)")
        if not files:
            return
        files = sorted(files, key=natural_sort_key)
        self.image_paths = files
        self.list_widget.clear()
        for f in files:
            self.list_widget.addItem(f)
        self.lbl_info.setText(f"{len(files)} images loaded. Please set rows×cols accordingly.")

    def on_preview(self):
        if not self.image_paths:
            QtWidgets.QMessageBox.warning(self, "No images", "Please load tile images first.")
            return
        rows = int(self.spin_rows.value())
        cols = int(self.spin_cols.value())
        if rows * cols != len(self.image_paths):
            reply = QtWidgets.QMessageBox.question(self, "Mismatch", 
                f"Rows×Cols = {rows*cols} but {len(self.image_paths)} images loaded.\nContinue using first {rows*cols} images?",
                QtWidgets.QMessageBox.Yes | QtWidgets.QMessageBox.No, QtWidgets.QMessageBox.No)
            if reply == QtWidgets.QMessageBox.No:
                return
        try:
            ox = float(self.spin_ox.value())
            oy = float(self.spin_oy.value())
            imgs = self.image_paths[:rows*cols]
            self.log_status("Generating preview... (this may take a few seconds)")
            result, preview = stitch_grid_linear_blend(imgs, rows, cols, ox, oy, preview_scale=0.35, status_cb=self.log_status)
            if preview is None:
                disp = result
            else:
                disp = preview
            # convert to QImage
            h, w = disp.shape[:2]
            bytes_per_line = 3 * w
            qimg = QtGui.QImage(cv2.cvtColor(disp, cv2.COLOR_BGR2RGB).data, w, h, bytes_per_line, QtGui.QImage.Format_RGB888)
            pix = QtGui.QPixmap.fromImage(qimg)
            self.label_preview.setPixmap(pix.scaled(self.label_preview.size(), QtCore.Qt.KeepAspectRatio, QtCore.Qt.SmoothTransformation))
            self.log_status("Preview generated.")
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", str(e))
            self.log_status("")

    def on_stitch(self):
        if not self.image_paths:
            QtWidgets.QMessageBox.warning(self, "No images", "Please load tile images first.")
            return
        rows = int(self.spin_rows.value())
        cols = int(self.spin_cols.value())
        if rows * cols != len(self.image_paths):
            reply = QtWidgets.QMessageBox.question(self, "Mismatch", 
                f"Rows×Cols = {rows*cols} but {len(self.image_paths)} images loaded.\nContinue using first {rows*cols} images?",
                QtWidgets.QMessageBox.Yes | QtWidgets.QMessageBox.No, QtWidgets.QMessageBox.No)
            if reply == QtWidgets.QMessageBox.No:
                return
        save_path, _ = QtWidgets.QFileDialog.getSaveFileName(self, "Save stitched image", os.path.join(os.getcwd(), "stitched.png"),
                                                             "PNG (*.png);;TIFF (*.tif *.tiff);;JPEG (*.jpg *.jpeg)")
        if not save_path:
            return
        try:
            ox = float(self.spin_ox.value())
            oy = float(self.spin_oy.value())
            imgs = self.image_paths[:rows*cols]
            self.log_status("Stitching full resolution... (this can take time for large images)")
            result, _ = stitch_grid_linear_blend(imgs, rows, cols, ox, oy, preview_scale=None, status_cb=self.log_status)
            # save via cv2
            ok = cv2.imwrite(save_path, result)
            if not ok:
                raise IOError("Failed to write output file.")
            QtWidgets.QMessageBox.information(self, "Saved", f"Stitched mosaic saved to:\n{save_path}")
            # show result scaled down
            h, w = result.shape[:2]
            max_display = 1200
            scale = min(1.0, max_display / max(w, h))
            disp = cv2.resize(result, (int(w * scale), int(h * scale)), interpolation=cv2.INTER_AREA)
            bytes_per_line = 3 * disp.shape[1]
            qimg = QtGui.QImage(cv2.cvtColor(disp, cv2.COLOR_BGR2RGB).data, disp.shape[1], disp.shape[0], bytes_per_line, QtGui.QImage.Format_RGB888)
            pix = QtGui.QPixmap.fromImage(qimg)
            self.label_preview.setPixmap(pix.scaled(self.label_preview.size(), QtCore.Qt.KeepAspectRatio, QtCore.Qt.SmoothTransformation))
            self.log_status("Stitching finished.")
        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "Error", str(e))
            self.log_status("")


def main():
    app = QtWidgets.QApplication(sys.argv)
    win = GridStitcherApp()
    win.show()
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
