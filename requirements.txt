# MIST GUI Requirements
# Python packages required for the MIST GUI application

# Core GUI framework
tkinter  # Usually included with Python

# Image processing
Pillow>=9.0.0

# Scientific computing (required for MIST Python)
numpy>=1.20.0
scipy>=1.7.0

# Image processing for MIST Python
scikit-image>=0.18.0

# JSON handling (built-in)
# json

# File operations (built-in)
# os
# pathlib
# shutil
# tempfile

# Process management (built-in)
# subprocess
# threading

# Logging (built-in)
# logging

# Type hints (built-in for Python 3.5+)
# typing

# Optional: Enhanced image processing
# opencv-python>=4.5.0  # For advanced image operations

# Optional: Progress bars and UI enhancements
# tqdm>=4.60.0  # For command-line progress bars

# Optional: Configuration file formats
# pyyaml>=5.4.0  # For YAML config files
# toml>=0.10.0   # For TOML config files

# Development dependencies (optional)
# pytest>=6.0.0  # For testing
# black>=21.0.0   # For code formatting
# flake8>=3.8.0   # For linting
