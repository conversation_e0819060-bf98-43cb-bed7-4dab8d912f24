#!/usr/bin/env python3
"""
Complete MIST GUI Demo
Creates test images and runs the GUI for a complete demonstration

Author: AI Assistant
Date: 2025
"""

import os
import sys
import subprocess
import tkinter as tk
from tkinter import messagebox

def check_dependencies():
    """Check if all required dependencies are available"""
    missing_deps = []
    
    # Check PIL/Pillow
    try:
        from PIL import Image, ImageDraw, ImageFilter
        print("✓ Pillow (PIL) found")
    except ImportError:
        missing_deps.append("Pillow")
        print("✗ Pillow (PIL) not found")
    
    # Check numpy
    try:
        import numpy
        print("✓ NumPy found")
    except ImportError:
        missing_deps.append("numpy")
        print("✗ NumPy not found")
    
    return missing_deps

def create_test_images_if_needed():
    """Create test images if they don't exist"""
    test_dir = "test_images"
    
    if os.path.exists(test_dir) and os.listdir(test_dir):
        print(f"✓ Test images already exist in {test_dir}")
        return True
    
    print("Creating test images...")
    try:
        # Import and run the test image creator
        from create_test_images import create_different_test_sets
        create_different_test_sets()
        print("✓ Test images created successfully")
        return True
    except Exception as e:
        print(f"✗ Failed to create test images: {e}")
        return False

def show_demo_instructions():
    """Show instructions for using the demo"""
    instructions = """
MIST GUI Demo Instructions
==========================

Test images have been created in the 'test_images' directory.

To test the GUI:

1. INPUT/OUTPUT TAB:
   - Input Directory: Select one of these folders:
     • test_images/small_grid (3x2 grid, 256x256 images)
     • test_images/medium_grid (4x3 grid, 512x512 images) 
     • test_images/large_grid (6x4 grid, 512x512 images)
   
   - Output Directory: Choose any empty folder
   - Filename Pattern: tile_{r:02d}_{c:02d}.tif

2. GRID CONFIGURATION TAB:
   - Set grid dimensions according to your chosen test set:
     • small_grid: 3 width × 2 height
     • medium_grid: 4 width × 3 height
     • large_grid: 6 width × 4 height
   
   - Set overlap percentages (10-20% recommended)

3. ADVANCED SETTINGS TAB:
   - Leave default settings or experiment with different options

4. PROCESSING TAB:
   - Click "Start Stitching" to begin
   - Monitor progress and logs
   - Check output directory for results

DEMO MODE:
Since ImageJ/MIST may not be installed, the GUI will run in 
simulation mode and create a demonstration output image.

For real stitching, install ImageJ/Fiji with the MIST plugin.
"""
    
    print(instructions)
    
    # Also show in a GUI dialog
    root = tk.Tk()
    root.withdraw()  # Hide the root window
    
    messagebox.showinfo("MIST GUI Demo Instructions", instructions)
    root.destroy()

def main():
    """Main demo function"""
    print("MIST GUI Complete Demo")
    print("=" * 50)
    
    # Check Python version
    if sys.version_info < (3, 7):
        print(f"✗ Python 3.7+ required, found {sys.version_info.major}.{sys.version_info.minor}")
        input("Press Enter to exit...")
        return
    
    print(f"✓ Python version: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    
    # Check dependencies
    missing_deps = check_dependencies()
    if missing_deps:
        print(f"\n✗ Missing dependencies: {', '.join(missing_deps)}")
        print("Please install missing dependencies:")
        print("  pip install -r requirements.txt")
        input("Press Enter to exit...")
        return
    
    print("✓ All dependencies found")
    
    # Create test images
    if not create_test_images_if_needed():
        print("✗ Failed to create test images")
        input("Press Enter to exit...")
        return
    
    # Show instructions
    show_demo_instructions()
    
    # Check if GUI file exists
    gui_file = "mist_gui.py"
    if not os.path.exists(gui_file):
        print(f"✗ {gui_file} not found")
        input("Press Enter to exit...")
        return
    
    print(f"✓ Found {gui_file}")
    
    # Start the GUI
    print("\nStarting MIST GUI...")
    print("=" * 50)
    
    try:
        # Add current directory to Python path
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        # Import and run the GUI
        from mist_gui import main as run_gui
        run_gui()
        
    except KeyboardInterrupt:
        print("\nDemo interrupted by user")
    except Exception as e:
        print(f"✗ Error running GUI: {e}")
        import traceback
        traceback.print_exc()
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
