#!/usr/bin/env python3
"""
MIST GUI Launcher
Simple launcher script for the MIST GUI application

Author: AI Assistant
Date: 2025
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

def check_dependencies():
    """Check if all required dependencies are available"""
    missing_deps = []
    
    # Check PIL/Pillow
    try:
        from PIL import Image, ImageTk
    except ImportError:
        missing_deps.append("Pillow (PIL)")
    
    # Check numpy
    try:
        import numpy
    except ImportError:
        missing_deps.append("numpy")
    
    return missing_deps

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 7):
        return False, f"Python 3.7+ required, found {sys.version_info.major}.{sys.version_info.minor}"
    return True, ""

def main():
    """Main launcher function"""
    print("MIST GUI Launcher")
    print("=" * 50)
    
    # Check Python version
    version_ok, version_msg = check_python_version()
    if not version_ok:
        print(f"ERROR: {version_msg}")
        input("Press Enter to exit...")
        return
    
    print(f"✓ Python version: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    
    # Check dependencies
    missing_deps = check_dependencies()
    if missing_deps:
        print("ERROR: Missing required dependencies:")
        for dep in missing_deps:
            print(f"  - {dep}")
        print("\nPlease install missing dependencies:")
        print("  pip install -r requirements.txt")
        input("Press Enter to exit...")
        return
    
    print("✓ All dependencies found")
    
    # Check if main GUI file exists
    gui_file = "mist_gui.py"
    if not os.path.exists(gui_file):
        print(f"ERROR: {gui_file} not found in current directory")
        print("Please ensure all files are in the same directory")
        input("Press Enter to exit...")
        return
    
    print(f"✓ Found {gui_file}")
    
    # Try to import and run the GUI
    try:
        print("\nStarting MIST GUI...")
        
        # Add current directory to Python path
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        # Import and run the GUI
        from mist_gui import main as run_gui
        run_gui()
        
    except ImportError as e:
        print(f"ERROR: Failed to import GUI modules: {e}")
        print("Please check that all files are present and dependencies are installed")
        input("Press Enter to exit...")
    except Exception as e:
        print(f"ERROR: Failed to start GUI: {e}")
        print("Please check the error message above for details")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
