#!/usr/bin/env python3
"""
Test MIST Integration
Test script to verify MIST Python integration works correctly

Author: AI Assistant
Date: 2025
"""

import os
import sys
import shutil
from mist_integration import MISTIntegration

def test_mist_integration():
    """Test MIST integration with sample data"""
    
    print("Testing MIST Python Integration")
    print("=" * 50)
    
    # Create test configuration
    config = {
        'input_directory': 'test_images/small_grid',
        'output_directory': 'output_test_mist',
        'filename_pattern': 'tile_{r:02d}_{c:02d}.tif',
        'grid_width': 3,
        'grid_height': 2,
        'horizontal_overlap': 10.0,
        'vertical_overlap': 10.0,
        'stage_repeatability': 2.0,
        'output_format': 'TIF'
    }
    
    # Check if test images exist
    if not os.path.exists(config['input_directory']):
        print(f"❌ Test images not found: {config['input_directory']}")
        print("Please run: python create_test_images.py")
        return False
    
    print(f"✓ Test images found: {config['input_directory']}")
    
    # Clean output directory if exists
    if os.path.exists(config['output_directory']):
        try:
            shutil.rmtree(config['output_directory'])
            print(f"✓ Cleaned output directory: {config['output_directory']}")
        except Exception as e:
            print(f"⚠ Warning: Could not clean output directory: {e}")

    # Use a unique output directory name to avoid conflicts
    import time
    timestamp = int(time.time())
    config['output_directory'] = f"output_test_mist_{timestamp}"
    
    # Create MIST integration
    integration = MISTIntegration()
    
    # Validate installation
    print("\nValidating MIST Python installation...")
    validation = integration.validate_installation()
    
    for key, value in validation.items():
        status = "✓" if value else "❌"
        print(f"{status} {key}: {value}")
    
    if not all(validation.values()):
        print("\n❌ MIST validation failed")
        return False
    
    print("\n✓ MIST validation successful")
    
    # Test stitching
    print(f"\nRunning MIST stitching...")
    print(f"Input: {config['input_directory']}")
    print(f"Output: {config['output_directory']}")
    print(f"Grid: {config['grid_width']} x {config['grid_height']}")
    print(f"Pattern: {config['filename_pattern']}")
    
    def progress_callback(value):
        print(f"Progress: {value}%")
    
    def log_callback(message):
        print(f"LOG: {message}")
    
    try:
        success = integration.run_stitching(
            config, 
            progress_callback=progress_callback,
            log_callback=log_callback
        )
        
        if success:
            print("\n✓ MIST stitching completed successfully!")
            
            # Check output files
            if os.path.exists(config['output_directory']):
                files = os.listdir(config['output_directory'])
                print(f"✓ Output directory created with {len(files)} files:")
                for file in files:
                    print(f"  - {file}")
                return True
            else:
                print("❌ Output directory not created")
                return False
        else:
            print("❌ MIST stitching failed")
            return False
            
    except Exception as e:
        print(f"❌ Error during stitching: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    success = test_mist_integration()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 MIST Integration Test: PASSED")
        print("\nYour MIST Python integration is working correctly!")
        print("You can now use the GUI with real MIST processing.")
    else:
        print("❌ MIST Integration Test: FAILED")
        print("\nPlease check the error messages above.")
        print("The GUI will fall back to simulation mode.")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
