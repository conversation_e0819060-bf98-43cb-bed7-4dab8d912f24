#!/usr/bin/env python3
"""
Test MIST with Real Data
Test script to verify MIST Python works with real microscopy data

Author: AI Assistant
Date: 2025
"""

import os
import sys
import shutil
from mist_integration import MISTIntegration

def test_real_data():
    """Test MIST integration with real microscopy data"""
    
    print("Testing MIST Python with Real Data")
    print("=" * 50)
    
    # Configuration for real data
    config = {
        'input_directory': 'D:/Stitch/bilinear_stitching_20250915_152529',
        'output_directory': 'D:/Stitch/bilinear_stitching_20250915_152529/mist_output',
        'filename_pattern': 'tile_{r}_{c}.png',
        'grid_width': 9,
        'grid_height': 4,
        'horizontal_overlap': 15.0,
        'vertical_overlap': 15.0,
        'stage_repeatability': 3.0,
        'output_format': 'TIF'
    }
    
    # Check if input directory exists
    if not os.path.exists(config['input_directory']):
        print(f"❌ Input directory not found: {config['input_directory']}")
        print("Please update the path to your microscopy data")
        return False
    
    print(f"✓ Input directory found: {config['input_directory']}")
    
    # List some files to verify pattern
    files = [f for f in os.listdir(config['input_directory']) if f.endswith('.png')]
    print(f"✓ Found {len(files)} PNG files")
    if files:
        print(f"  Sample files: {files[:3]}...")
    
    # Clean output directory if exists
    if os.path.exists(config['output_directory']):
        try:
            shutil.rmtree(config['output_directory'])
            print(f"✓ Cleaned output directory: {config['output_directory']}")
        except Exception as e:
            print(f"⚠ Warning: Could not clean output directory: {e}")
    
    # Create MIST integration
    integration = MISTIntegration()
    
    # Validate installation
    print("\nValidating MIST Python installation...")
    validation = integration.validate_installation()
    
    for key, value in validation.items():
        status = "✓" if value else "❌"
        print(f"{status} {key}: {value}")
    
    if not all(validation.values()):
        print("\n❌ MIST validation failed")
        return False
    
    print("\n✓ MIST validation successful")
    
    # Test stitching
    print(f"\nRunning MIST stitching on real data...")
    print(f"Input: {config['input_directory']}")
    print(f"Output: {config['output_directory']}")
    print(f"Grid: {config['grid_width']} x {config['grid_height']}")
    print(f"Pattern: {config['filename_pattern']}")
    
    def progress_callback(value):
        print(f"Progress: {value}%")
    
    def log_callback(message):
        print(f"LOG: {message}")
    
    try:
        success = integration.run_stitching(
            config, 
            progress_callback=progress_callback,
            log_callback=log_callback
        )
        
        if success:
            print("\n✓ MIST stitching completed successfully!")
            
            # Check output files
            if os.path.exists(config['output_directory']):
                files = os.listdir(config['output_directory'])
                print(f"✓ Output directory created with {len(files)} files:")
                for file in files:
                    file_path = os.path.join(config['output_directory'], file)
                    size = os.path.getsize(file_path)
                    print(f"  - {file} ({size:,} bytes)")
                return True
            else:
                print("❌ Output directory not created")
                return False
        else:
            print("❌ MIST stitching failed")
            return False
            
    except Exception as e:
        print(f"❌ Error during stitching: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    success = test_real_data()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Real Data Test: PASSED")
        print("\nYour MIST Python integration works with real data!")
        print("The GUI will now show real stitching results.")
    else:
        print("❌ Real Data Test: FAILED")
        print("\nPlease check the error messages above.")
        print("The GUI will fall back to demo mode.")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
